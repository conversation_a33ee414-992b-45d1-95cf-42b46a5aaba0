
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ignition Gateway Monitor</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #1e1e2e 0%, #2d2d44 100%); color: #e0e0e0; min-height: 100vh; padding: 20px; }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; padding: 20px; background: rgba(255, 255, 255, 0.05); border-radius: 15px; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1); }
        .header h1 { color: #4fc3f7; margin-bottom: 10px; font-size: 2.5em; text-shadow: 0 0 20px rgba(79, 195, 247, 0.3); }
        .header p { color: #b0b0b0; font-size: 1.1em; }
        .controls { display: flex; justify-content: center; gap: 15px; margin-bottom: 30px; }
        .btn { background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%); color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-size: 1em; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3); }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(79, 195, 247, 0.4); }
        .btn:active { transform: translateY(0); }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .gateway-card { background: rgba(255, 255, 255, 0.08); border-radius: 15px; padding: 20px; border: 1px solid rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); transition: all 0.3s ease; position: relative; overflow: hidden; }
        .gateway-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #4fc3f7, #29b6f6); opacity: 0; transition: opacity 0.3s ease; }
        .gateway-card.online::before { background: linear-gradient(90deg, #4caf50, #66bb6a); opacity: 1; }
        .gateway-card.offline::before { background: linear-gradient(90deg, #f44336, #ef5350); opacity: 1; }
        .gateway-card:hover { transform: translateY(-5px); box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3); }
        .gateway-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .gateway-name { font-size: 1.2em; font-weight: 600; color: #4fc3f7; }
        .gateway-type { background: rgba(79, 195, 247, 0.2); color: #4fc3f7; padding: 4px 12px; border-radius: 20px; font-size: 0.8em; font-weight: 500; }
        .status-item { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; padding: 8px; background: rgba(255, 255, 255, 0.03); border-radius: 8px; }
        .status-label { color: #b0b0b0; font-weight: 500; }
        .status-value { font-weight: 600; }
        .status-online { color: #4caf50; }
        .status-offline { color: #f44336; }
        .status-neutral { color: #ff9800; }
        .last-updated { text-align: center; color: #888; font-size: 0.9em; margin-top: 20px; padding: 15px; background: rgba(255, 255, 255, 0.05); border-radius: 10px; }
        .loading { text-align: center; color: #4fc3f7; font-size: 1.2em; padding: 40px; }
        .spinner { border: 3px solid rgba(79, 195, 247, 0.3); border-top: 3px solid #4fc3f7; border-radius: 50%; width: 30px; height: 30px; animation: spin 1s linear infinite; margin: 0 auto 15px; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .summary { display: flex; justify-content: center; gap: 30px; margin-bottom: 30px; flex-wrap: wrap; }
        .summary-item { background: rgba(255, 255, 255, 0.08); padding: 20px; border-radius: 15px; text-align: center; min-width: 120px; border: 1px solid rgba(255, 255, 255, 0.1); }
        .summary-number { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .summary-online .summary-number { color: #4caf50; }
        .summary-offline .summary-number { color: #f44336; }
        .summary-total .summary-number { color: #4fc3f7; }
        @media (max-width: 768px) { .status-grid { grid-template-columns: 1fr; } .summary { gap: 15px; } .controls { flex-direction: column; align-items: center; } }
        #countdownContainer { margin-top: 10px; }
        #refreshingAnimation { display: flex; align-items: center; justify-content: center; gap: 10px; color: #4fc3f7; font-weight: 500; }
        .refresh-spinner { border: 2px solid rgba(79, 195, 247, 0.3); border-top: 2px solid #4fc3f7; border-radius: 50%; width: 16px; height: 16px; animation: refresh-spin 0.8s linear infinite; }
        @keyframes refresh-spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        #countdown { font-weight: bold; color: #4fc3f7; transition: color 0.3s ease; }
        #countdown.low { color: #ff9800; animation: pulse 1s ease-in-out infinite; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.6; } }
        .last-updated { text-align: center; color: #888; font-size: 0.9em; margin-top: 20px; padding: 15px; background: rgba(255, 255, 255, 0.05); border-radius: 10px; border: 1px solid rgba(255, 255, 255, 0.1); }
        .last-updated div:first-child { margin-bottom: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>OT Team Gateway Monitor</h1>
            <p>Monitoring of gateway infrastructure</p>
        </div>

        <div class="controls">
            <button class="btn" onclick="refreshStatus()">🔄 Refresh All</button>
            <button class="btn" onclick="toggleAutoRefresh()">⏱️ Auto Refresh: <span id="autoRefreshStatus">ON</span></button>
            <a class="btn" href="/pisauron">👁️ Pi of Sauron</a>
            <a class="btn" href="/logs">📄 Logs</a>
        </div>

        <div class="summary" id="summary">
            <!-- Summary will be populated by JavaScript -->
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            Checking gateway status...
        </div>

        <div class="status-grid" id="statusGrid" style="display: none;">
            <!-- Gateway cards will be populated by JavaScript -->
        </div>

        <div class="last-updated" id="lastUpdated" style="display: none;">
            <!-- Last updated info will be populated by JavaScript -->
        </div>
    </div>

    <script>
        let autoRefresh = true;
        let refreshInterval;
        let countdownInterval;
        let countdownSeconds = 30;

        function updateStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('statusGrid').style.display = 'grid';
                    document.getElementById('lastUpdated').style.display = 'block';
                    document.getElementById('summary').style.display = 'flex';
                    
                    updateSummary(data);
                    updateGatewayCards(data);
                    updateLastUpdated();
                    
                    hideRefreshingAnimation();
                    if (autoRefresh) {
                        startCountdown();
                    }
                })
                .catch(error => {
                    console.error('Error fetching status:', error);
                    hideRefreshingAnimation();
                    if (autoRefresh) {
                        startCountdown();
                    }
                });
        }

        function updateSummary(data) {
            const gateways = Object.values(data);
            const total = gateways.length;
            const online = gateways.filter(g => g.overall_status === 'online').length;
            const offline = total - online;

            document.getElementById('summary').innerHTML = `
                <div class="summary-item summary-total">
                    <div class="summary-number">${total}</div>
                    <div>Total</div>
                </div>
                <div class="summary-item summary-online">
                    <div class="summary-number">${online}</div>
                    <div>Online</div>
                </div>
                <div class="summary-item summary-offline">
                    <div class="summary-number">${offline}</div>
                    <div>Offline</div>
                </div>
            `;
        }

        function updateGatewayCards(data) {
            const statusGrid = document.getElementById('statusGrid');
            statusGrid.innerHTML = '';

            const sortedGateways = Object.values(data).sort((a, b) => a.order - b.order);

            sortedGateways.forEach(gateway => {
                const card = document.createElement('div');
                card.className = `gateway-card ${gateway.overall_status}`;
                
                const hostStatus = gateway.host_online ? 'online' : 'offline';
                const serviceStatus = gateway.service_running === true ? 'online' : 
                                    gateway.service_running === false ? 'offline' : 'neutral';
                
                let portInfo = `${gateway.host}`;
                if (gateway.web_port) {
                    portInfo += `:${gateway.web_port}`;
                } else {
                    portInfo += `:${gateway.port}`;
                }
                
                card.innerHTML = `
                    <div class="gateway-header">
                        <div class="gateway-name">${gateway.name}</div>
                        <div class="gateway-type">${gateway.type}</div>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Host Ping (${gateway.host})</span>
                        <span class="status-value status-${hostStatus}">
                            ${gateway.host_online ? '🟢 Reachable' : '🔴 Unreachable'}
                        </span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Service (${portInfo})</span>
                        <span class="status-value status-${serviceStatus}">
                            ${gateway.service_status}
                        </span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Last Check</span>
                        <span class="status-value">${gateway.last_check}</span>
                    </div>
                `;
                
                statusGrid.appendChild(card);
            });
        }

        function updateLastUpdated() {
            const now = new Date();
            const lastUpdatedElement = document.getElementById('lastUpdated');
            
            if (autoRefresh) {
                lastUpdatedElement.innerHTML = `
                    <div>Last updated: ${now.toLocaleTimeString()}</div>
                    <div id="countdownContainer">
                        <span id="countdownText">Next refresh in <span id="countdown">${countdownSeconds}</span> seconds</span>
                        <div id="refreshingAnimation" style="display: none;">
                            <div class="refresh-spinner"></div>
                            <span>Refreshing...</span>
                        </div>
                    </div>
                `;
            } else {
                lastUpdatedElement.innerHTML = `Last updated: ${now.toLocaleTimeString()} | Auto refresh is OFF`;
            }
        }

        function startCountdown() {
            clearInterval(countdownInterval);
            countdownSeconds = 30;
            
            const countdownElement = document.getElementById('countdown');
            if (countdownElement) {
                countdownElement.textContent = countdownSeconds;
                countdownElement.classList.remove('low');
            }
            
            countdownInterval = setInterval(() => {
                countdownSeconds--;
                const countdownEl = document.getElementById('countdown');
                if (countdownEl) {
                    countdownEl.textContent = countdownSeconds;
                    
                    if (countdownSeconds <= 5) {
                        countdownEl.classList.add('low');
                    } else {
                        countdownEl.classList.remove('low');
                    }
                }
                
                if (countdownSeconds <= 0) {
                    clearInterval(countdownInterval);
                    showRefreshingAnimation();
                    fetch('/api/refresh')
                        .then(response => response.json())
                        .then(data => {
                            setTimeout(updateStatus, 1000);
                        })
                        .catch(error => {
                            console.error('Error refreshing status:', error);
                            updateStatus();
                        });
                }
            }, 1000);
        }

        function showRefreshingAnimation() {
            const countdownText = document.getElementById('countdownText');
            const refreshingAnimation = document.getElementById('refreshingAnimation');
            
            if (countdownText && refreshingAnimation) {
                countdownText.style.display = 'none';
                refreshingAnimation.style.display = 'flex';
            }
        }

        function hideRefreshingAnimation() {
            const countdownText = document.getElementById('countdownText');
            const refreshingAnimation = document.getElementById('refreshingAnimation');
            
            if (countdownText && refreshingAnimation) {
                countdownText.style.display = 'inline';
                refreshingAnimation.style.display = 'none';
            }
        }

        function refreshStatus() {
            clearInterval(countdownInterval);
            
            document.getElementById('loading').style.display = 'block';
            document.getElementById('statusGrid').style.display = 'none';
            
            showRefreshingAnimation();
            
            fetch('/api/refresh')
                .then(response => response.json())
                .then(data => {
                    setTimeout(updateStatus, 1000);
                })
                .catch(error => {
                    console.error('Error refreshing status:', error);
                    updateStatus();
                });
        }

        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            document.getElementById('autoRefreshStatus').textContent = autoRefresh ? 'ON' : 'OFF';
            
            if (autoRefresh) {
                startAutoRefresh();
                updateLastUpdated();
                startCountdown();
            } else {
                clearInterval(refreshInterval);
                clearInterval(countdownInterval);
                updateLastUpdated();
            }
        }

        function startAutoRefresh() {
            refreshInterval = setInterval(updateStatus, 30000);
        }

        updateStatus();
        if (autoRefresh) {
            startAutoRefresh();
        }
    </script>
</body>
</html>
