#!/bin/bash
# Pi Kiosk Agent Upgrade Script

echo "Upgrading Pi Kiosk Agent..."

# Stop services during upgrade
sudo systemctl stop kiosk-agent.service kiosk-monitor.service 2>/dev/null

# 1. Add monitor log endpoint to kiosk_agent.py
echo "Adding monitor log endpoint..."
if ! grep -q "monitor-log" /opt/kiosk-agent/kiosk_agent.py; then
    sudo sed -i '/# ======= PHONE HOME LOOP =======/i\
@app.route("/api/agent/monitor-log")\
def api_monitor_log():\
    if not check_auth(request): return ("forbidden", 403)\
    try:\
        with open('\''/var/log/kiosk-monitor.log'\'', '\''r'\'') as f:\
            lines = f.readlines()\
        # Return last 100 lines\
        recent_lines = lines[-100:] if len(lines) > 100 else lines\
        return jsonify({"log_lines": recent_lines})\
    except Exception as e:\
        return jsonify({"error": str(e)}), 500\
' /opt/kiosk-agent/kiosk_agent.py
fi

# 2. Fix Flask host binding
echo "Fixing Flask host binding..."
sudo sed -i 's/app.run(host="127.0.0.1"/app.run(host="0.0.0.0"/' /opt/kiosk-agent/kiosk_agent.py

# 3. Create upgraded kiosk monitor
echo "Creating kiosk monitor..."
sudo tee /opt/kiosk-agent/kiosk_monitor.py << 'EOF'
#!/usr/bin/env python3
import os
import sys
import time
import subprocess
import requests
import socket
import json
from datetime import datetime

# Configuration
CHECK_INTERVAL = 60  # seconds between health checks
NETWORK_TIMEOUT = 10  # seconds for network tests
REBOOT_THRESHOLD = 3  # failed checks before reboot
LOG_FILE = "/var/log/kiosk-monitor.log"

# URLs and endpoints to check (2 out of 4 must pass)
CONNECTIVITY_TESTS = [
    ("Ignition Gateway", "https://************:8043/data/perspective/client/REVMES_Powered/countdownClock/"),
    ("Pi of Sauron Server", "http://*************:5000"),
    ("Internet Connectivity", "https://www.google.com"),
    ("Network Gateway", "ping://***********"),  # Special ping test
]
MIN_CONNECTIVITY_REQUIRED = 2  # At least 2 of 4 must pass

class KioskMonitor:
    def __init__(self):
        self.failed_checks = 0
        
    def log(self, message, level="INFO"):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_msg = f"[{timestamp}] {level}: {message}"
        print(log_msg)
        
        try:
            with open(LOG_FILE, "a") as f:
                f.write(log_msg + "\n")
        except:
            pass
    
    def run_command(self, cmd, timeout=10):
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, 
                                  text=True, timeout=timeout)
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", "Timeout"
        except Exception as e:
            return False, "", str(e)
    
    def check_network_connectivity(self):
        """Test basic network connectivity with flexible requirements"""
        passed_tests = 0
        total_tests = len(CONNECTIVITY_TESTS)
        
        for test_name, test_target in CONNECTIVITY_TESTS:
            try:
                if test_target.startswith("ping://"):
                    # Ping test
                    host = test_target.replace("ping://", "")
                    success, _, _ = self.run_command(f"ping -c 2 -W 3 {host}", timeout=10)
                    if success:
                        passed_tests += 1
                        self.log(f"Connectivity test passed: {test_name} ({host})", "DEBUG")
                    else:
                        self.log(f"Connectivity test failed: {test_name} ({host}) - ping failed", "ERROR")
                else:
                    # HTTP/HTTPS test
                    response = requests.get(test_target, timeout=NETWORK_TIMEOUT, 
                                          verify=False, allow_redirects=True)
                    if response.status_code < 500:
                        passed_tests += 1
                        self.log(f"Connectivity test passed: {test_name}", "DEBUG")
                    else:
                        self.log(f"Connectivity test failed: {test_name} - HTTP {response.status_code}", "ERROR")
            except Exception as e:
                self.log(f"Connectivity test failed: {test_name} - {e}", "ERROR")
        
        success = passed_tests >= MIN_CONNECTIVITY_REQUIRED
        self.log(f"Connectivity summary: {passed_tests}/{total_tests} tests passed (need {MIN_CONNECTIVITY_REQUIRED})", 
                "DEBUG" if success else "ERROR")
        
        return success
    
    def check_chromium_running(self):
        """Check if Chromium is running and responsive"""
        try:
            # Check if chromium process exists
            success, output, _ = self.run_command("pgrep -f chromium-browser")
            if not success:
                self.log("Chromium process not found", "ERROR")
                return False
            
            # Check if debugging port is responding
            try:
                response = requests.get("http://127.0.0.1:9222/json", timeout=5)
                if response.status_code == 200:
                    targets = response.json()
                    if not targets:
                        self.log("No Chromium targets found", "ERROR")
                        return False
                    return True
            except:
                self.log("Chromium debugging port not responding", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Chromium check failed: {e}", "ERROR")
            return False
    
    def check_weston_running(self):
        """Check if Weston is running"""
        try:
            success, _, _ = self.run_command("pgrep -f weston")
            if not success:
                self.log("Weston process not found", "ERROR")
                return False
            
            # Check if wayland socket exists
            if not os.path.exists("/run/user/1000/wayland-0"):
                self.log("Wayland socket not found", "ERROR")
                return False
                
            return True
        except Exception as e:
            self.log(f"Weston check failed: {e}", "ERROR")
            return False
    
    def check_services_status(self):
        """Check if systemd services are active"""
        services = ["<EMAIL>", "kiosk.service"]
        
        for service in services:
            success, output, _ = self.run_command(f"systemctl is-active {service}")
            if not success or "active" not in output:
                self.log(f"Service {service} not active", "ERROR")
                return False
        return True
    
    def perform_health_check(self):
        """Perform comprehensive health check"""
        checks = [
            ("Network Connectivity", self.check_network_connectivity),
            ("Weston Running", self.check_weston_running),
            ("Chromium Running", self.check_chromium_running),
            ("Services Status", self.check_services_status),
        ]
        
        failed_checks = []
        
        for check_name, check_func in checks:
            try:
                if not check_func():
                    failed_checks.append(check_name)
                    self.log(f"Health check failed: {check_name}", "ERROR")
                else:
                    self.log(f"Health check passed: {check_name}", "DEBUG")
            except Exception as e:
                failed_checks.append(check_name)
                self.log(f"Health check error {check_name}: {e}", "ERROR")
        
        return len(failed_checks) == 0, failed_checks
    
    def restart_services(self):
        """Attempt to restart services before full reboot"""
        self.log("Attempting service restart", "INFO")
        
        commands = [
            "<NAME_EMAIL>",
            "sleep 3", 
            "systemctl restart kiosk.service",
            "systemctl restart kiosk-agent.service"
        ]
        
        for cmd in commands:
            success, _, error = self.run_command(f"sudo {cmd}", timeout=30)
            if not success:
                self.log(f"Service restart failed: {cmd} - {error}", "ERROR")
                return False
        
        self.log("Services restarted successfully", "INFO")
        return True
    
    def reboot_system(self):
        """Reboot the system"""
        self.log("REBOOTING SYSTEM due to persistent failures", "CRITICAL")
        subprocess.run(["sudo", "/sbin/reboot"], timeout=10)
    
    def run(self):
        self.log("Kiosk Monitor started", "INFO")
        
        while True:
            try:
                healthy, failed_checks = self.perform_health_check()
                
                if healthy:
                    if self.failed_checks > 0:
                        self.log(f"System recovered after {self.failed_checks} failed checks", "INFO")
                    self.failed_checks = 0
                else:
                    self.failed_checks += 1
                    self.log(f"Health check failed ({self.failed_checks}/{REBOOT_THRESHOLD}): {', '.join(failed_checks)}", "WARNING")
                    
                    if self.failed_checks >= REBOOT_THRESHOLD:
                        # Try service restart first (only once)
                        if self.failed_checks == REBOOT_THRESHOLD:
                            if self.restart_services():
                                self.log("Service restart attempted, monitoring continues", "INFO")
                                time.sleep(30)  # Give services time to start
                                continue
                        
                        # If we reach here, services restart failed or this is a subsequent failure
                        self.reboot_system()
                        break  # This line won't execute due to reboot, but good practice
                
                time.sleep(CHECK_INTERVAL)
                
            except KeyboardInterrupt:
                self.log("Monitor stopped by user", "INFO")
                break
            except Exception as e:
                self.log(f"Monitor error: {e}", "ERROR")
                time.sleep(CHECK_INTERVAL)

if __name__ == "__main__":
    monitor = KioskMonitor()
    monitor.run()
EOF

sudo chown display:display /opt/kiosk-agent/kiosk_monitor.py
sudo chmod +x /opt/kiosk-agent/kiosk_monitor.py

# 4. Create monitor service
echo "Creating monitor service..."
sudo tee /etc/systemd/system/kiosk-monitor.service << 'EOF'
[Unit]
Description=Kiosk Health Monitor and Auto-Recovery
After=kiosk.service kiosk-agent.service
Wants=kiosk.service kiosk-agent.service

[Service]
Type=simple
User=display
Group=display
Environment=XDG_RUNTIME_DIR=/run/user/1000
ExecStart=/opt/kiosk-agent/venv/bin/python /opt/kiosk-agent/kiosk_monitor.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# Allow the service to run system commands for restart/reboot
NoNewPrivileges=false

[Install]
WantedBy=multi-user.target
EOF

# 5. Setup sudo permissions for monitor
echo "Setting up sudo permissions..."
sudo tee /etc/sudoers.d/kiosk-monitor << 'EOF'
display ALL=(ALL) NOPASSWD: /bin/<NAME_EMAIL>
display ALL=(ALL) NOPASSWD: /bin/systemctl restart kiosk.service  
display ALL=(ALL) NOPASSWD: /bin/systemctl restart kiosk-agent.service
display ALL=(ALL) NOPASSWD: /sbin/reboot
EOF

# 6. Create log file with proper permissions
echo "Setting up log file..."
sudo touch /var/log/kiosk-monitor.log
sudo chown display:display /var/log/kiosk-monitor.log
sudo chmod 644 /var/log/kiosk-monitor.log

# 7. Setup log rotation
sudo tee /etc/logrotate.d/kiosk-monitor << 'EOF'
/var/log/kiosk-monitor.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    copytruncate
    create 644 display display
}
EOF

# 8. Install requests if not in venv yet
sudo -u display /opt/kiosk-agent/venv/bin/pip install requests >/dev/null 2>&1

# 9. Reload and start services
echo "Starting services..."
sudo systemctl daemon-reload
sudo systemctl enable kiosk-monitor.service
sudo systemctl start kiosk-agent.service
sleep 2
sudo systemctl start kiosk-monitor.service

# 10. Verify everything is working
echo "Verifying installation..."
sleep 3

echo "Service status:"
sudo systemctl is-active kiosk-agent.service
sudo systemctl is-active kiosk-monitor.service

echo "Testing endpoints:"
curl -s -H "X-Auth: F1n@lC0untd0wn" http://127.0.0.1:7070/api/agent/health >/dev/null && echo "Health endpoint: OK" || echo "Health endpoint: FAILED"
curl -s -H "X-Auth: F1n@lC0untd0wn" http://127.0.0.1:7070/api/agent/monitor-log >/dev/null && echo "Monitor log endpoint: OK" || echo "Monitor log endpoint: FAILED"

echo "Network accessibility test:"
netstat -tln | grep ":7070" | grep "0.0.0.0" >/dev/null && echo "External access: OK" || echo "External access: FAILED"

echo ""
echo "Pi Kiosk Agent upgrade completed!"
echo "Monitor log: /var/log/kiosk-monitor.log"
echo "Services will auto-restart on failure and reboot after 3 consecutive failures."