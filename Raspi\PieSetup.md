
## Complete Raspberry Pi 5 Kiosk Setup Guide

### 1. Initial System Setup and Package Installation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install core packages including Weston and Chromium
sudo apt install -y \
  weston libweston-10-0 libweston-10-dev \
  chromium-browser \
  python3 python3-pip python3-venv \
  curl wget git \
  network-manager \
  cec-utils

# Verify critical binaries are installed
which weston
which chromium-browser
weston --version
chromium-browser --version
```

### 2. User and Permissions Setup

```bash
# Create display user (if not exists)
sudo useradd -m -s /bin/bash display
sudo usermod -a -G video,render,input,tty,dialout,audio,plugdev,netdev,gpio,spi,i2c display

# Set up user directories and runtime
sudo -u display mkdir -p /home/<USER>/.config
sudo loginctl enable-linger display

# Disable Getty on TTY1 to prevent conflicts
sudo <NAME_EMAIL>
sudo <NAME_EMAIL>
```

### 3. Create Weston Service

```bash
sudo tee /etc/systemd/system/weston@.service << 'EOF'
[Unit]
Description=Weston on DRM/KMS (tty1) for %i
After=systemd-user-sessions.service
Conflicts=<EMAIL>
[Service]
User=%i
PAMName=login
TTYPath=/dev/tty1
TTYReset=yes
TTYVHangup=yes
TTYVTDisallocate=yes
StandardInput=tty
Environment=XDG_RUNTIME_DIR=/run/user/%U
ExecStart=/usr/bin/weston --backend=drm-backend.so --tty=1 --idle-time=0 --socket=wayland-0
Restart=always
RestartSec=5
[Install]
WantedBy=multi-user.target
EOF
```

### 4. Create Kiosk Service

```bash
# Update the URL for your specific station (change Pack/1/X for station number)
sudo tee /etc/systemd/system/kiosk.service << 'EOF'
[Unit]
Description=Chromium Wayland Kiosk
After=<EMAIL>
Requires=<EMAIL>
[Service]
User=display
Environment=XDG_RUNTIME_DIR=/run/user/1000
Environment=WAYLAND_DISPLAY=wayland-0
ExecStartPre=/bin/sleep 3
ExecStart=/usr/bin/chromium-browser \
  --user-data-dir=/home/<USER>/.config/chromium-kiosk \
  --remote-debugging-address=127.0.0.1 \
  --remote-debugging-port=9222 \
  --remote-allow-origins=* \
  --kiosk "https://************:8043/data/perspective/client/REVMES_Powered/countdownClock/Pack/1/1" \
  --enable-features=UseOzonePlatform \
  --ozone-platform=wayland \
  --use-gl=egl \
  --no-first-run \
  --noerrdialogs \
  --disable-session-crashed-bubble \
  --ignore-certificate-errors \
  --test-type \
  --disable-features=Translate,ChromeWhatsNewUI,AutofillServerCommunication \
  --disable-pinch \
  --overscroll-history-navigation=0 \
  --disable-3d-apis \
  --no-sandbox \
  --disable-dev-shm-usage \
  --disable-gpu-sandbox
Restart=always
RestartSec=2
[Install]
WantedBy=multi-user.target
EOF
```

### 5. Create Kiosk Agent Directory and Script

```bash
# Create kiosk agent directory
sudo mkdir -p /opt/kiosk-agent
sudo chown display:display /opt/kiosk-agent

# Create Python virtual environment
sudo -u display python3 -m venv /opt/kiosk-agent/venv
sudo -u display /opt/kiosk-agent/venv/bin/pip install flask requests websocket-client

# Create kiosk agent script
sudo tee /opt/kiosk-agent/kiosk_agent.py << 'EOF'
#!/usr/bin/env python3
import os, sys, json, time, base64, socket, subprocess, threading, requests, platform, shutil
from datetime import datetime
from flask import Flask, request, jsonify

# ======= CONFIG =======
AGENT_ID = os.environ.get("KIOSK_AGENT_ID", socket.gethostname())
SERVER = os.environ.get("KIOSK_SERVER_URL", "http://*************:5000")
SHARED_TOKEN = os.environ.get("KIOSK_SHARED_TOKEN", "F1n@lC0untd0wn")
INTERVAL_SEC = int(os.environ.get("KIOSK_REPORT_INTERVAL", "180"))

# Chromium remote debugging
CDP_PORT = int(os.environ.get("KIOSK_CDP_PORT", "9222"))
CHROMIUM_BIN = os.environ.get("KIOSK_CHROMIUM_BIN", "/usr/bin/chromium-browser")

# Services and logs to sample
SERVICE_NAMES = os.environ.get("KIOSK_SERVICE_NAMES", "kiosk.service;weston.service").split(";")
LOG_LINES = int(os.environ.get("KIOSK_LOG_LINES", "200"))

# Local API
LISTEN_PORT = int(os.environ.get("KIOSK_LOCAL_PORT", "7070"))

# ======= UTIL =======
def run(cmd, timeout=10, user_env=None):
    try:
        return subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout, env=user_env)
    except Exception as e:
        return subprocess.CompletedProcess(cmd, 1, "", str(e))

def is_wayland():
    return True

def get_wifi_info():
    out = run("iw dev | awk '$1==\"Interface\"{print $2}'").stdout.strip()
    if not out:
        return {}
    iface = out.splitlines()[0]
    ssid = run(f"iwgetid {iface} -r").stdout.strip()
    rssi = run(f"iw dev {iface} link | awk '/signal/ {{print $2}}'").stdout.strip()
    ip = run(f"ip -4 addr show {iface} | awk '/inet /{{print $2}}' | cut -d/ -f1").stdout.strip()
    return {"iface": iface, "ssid": ssid, "rssi_dbm": rssi, "ip": ip}

def get_cpu_temp_c():
    paths = ["/sys/class/thermal/thermal_zone0/temp", "/sys/class/thermal/thermal_zone1/temp"]
    for p in paths:
        if os.path.exists(p):
            try:
                v = int(open(p).read().strip())
                return round(v/1000.0, 1)
            except:
                pass
    return None

def get_health():
    disk = shutil.disk_usage("/")
    mem = run("free -m | awk '/Mem:/ {print $2\",\"$3\",\"$7}'").stdout.strip().split(",")
    uptime = run("awk '{print int($1)}' /proc/uptime").stdout.strip()
    health = {
        "agent_id": AGENT_ID,
        "ts": datetime.utcnow().isoformat()+"Z",
        "os": platform.platform(),
        "kernel": platform.release(),
        "uptime_sec": int(uptime) if uptime.isdigit() else None,
        "cpu_temp_c": get_cpu_temp_c(),
        "disk_total_gb": round(disk.total/1024/1024/1024,1),
        "disk_used_gb": round((disk.total-disk.free)/1024/1024/1024,1),
        "mem_total_mb": int(mem[0]) if len(mem)>=1 and mem[0].isdigit() else None,
        "mem_used_mb": int(mem[1]) if len(mem)>=2 and mem[1].isdigit() else None,
        "mem_free_mb": int(mem[2]) if len(mem)>=3 and mem[2].isdigit() else None,
        "net": get_wifi_info(),
        "cec": detect_cec(),
        "display_mode": "wayland" if is_wayland() else "x11",
    }
    svc = {}
    for s in SERVICE_NAMES:
        s = s.strip()
        if not s: continue
        status = run(f"systemctl is-active {s}").stdout.strip()
        svc[s] = status or "unknown"
    health["services"] = svc
    return health

def tail_logs():
    logs = {}
    for s in SERVICE_NAMES:
        s = s.strip()
        if not s: continue
        logs[s] = run(f"journalctl -u {s} -n {LOG_LINES} --no-pager").stdout[-40000:]
    logs["kernel"] = run(f"dmesg | tail -n {LOG_LINES}").stdout
    return logs

def detect_cec():
    out = run("which cec-ctl").returncode == 0 or run("which cec-client").returncode == 0
    return {"available": bool(out)}

# ======= SCREENSHOT =======
import http.client
try:
    import websocket
except ImportError:
    websocket = None

def cdp_get_targets():
    try:
        conn = http.client.HTTPConnection("127.0.0.1", CDP_PORT, timeout=2)
        conn.request("GET", "/json")
        r = conn.getresponse()
        data = r.read()
        conn.close()
        arr = json.loads(data.decode("utf-8"))
        arr = [t for t in arr if t.get("type") == "page"]
        arr.sort(key=lambda t: (("kiosk" not in (t.get("title") or "").lower()), t.get("id")))
        return arr
    except Exception:
        return []

def cdp_capture_png(target_ws_url):
    if not websocket:
        return None
    try:
        ws = websocket.create_connection(target_ws_url, timeout=15)
        mid = 0
        def send(m):
            nonlocal mid; mid += 1; m["id"]=mid; ws.send(json.dumps(m)); return mid
        def recv_until(i, t=15):
            import time
            end = time.time() + t
            while time.time() < end:
                resp = json.loads(ws.recv())
                if resp.get("id")==i: return resp
            raise TimeoutError("cdp timeout")
        send({"method":"Page.enable"})
        send({"method":"Page.bringToFront"})
        rid = send({"method":"Page.captureScreenshot","params":{"format":"png"}})
        resp = recv_until(rid)
        ws.close()
        if "result" in resp and "data" in resp["result"]:
            return base64.b64decode(resp["result"]["data"])
    except Exception:
        pass
    return None

def grab_screenshot():
    targets = cdp_get_targets()
    for t in targets:
        wsurl = t.get("webSocketDebuggerUrl")
        if not wsurl:
            continue
        try:
            png = cdp_capture_png(wsurl)
            if png:
                return png
        except Exception:
            pass

    try:
        import tempfile, os
        env = os.environ.copy()
        env.setdefault("WAYLAND_DISPLAY", "wayland-0")
        tmpdir = tempfile.gettempdir()
        out = os.path.join(tmpdir, f"shot-{int(time.time())}.png")
        r = run(f"/usr/bin/weston-screenshooter {out}", timeout=6, user_env=env)
        if r.returncode == 0 and os.path.exists(out) and os.path.getsize(out) > 0:
            with open(out, "rb") as f:
                data = f.read()
            os.remove(out)
            return data
    except Exception:
        pass
    return None

# ======= COMMAND EXECUTION =======
def _ok():
    return {"ok": True, "ts": datetime.utcnow().isoformat()+"Z"}

def action_refresh():
    targets = cdp_get_targets()
    for t in targets:
        wsurl = t.get("webSocketDebuggerUrl")
        if not wsurl: continue
        try:
            if websocket:
                ws = websocket.create_connection(wsurl, timeout=3)
                ws.send(json.dumps({"id":1,"method":"Page.reload","params":{"ignoreCache":True}}))
                ws.close()
                return _ok()
        except Exception: pass
    return {"ok": False, "error": "no-devtools-target"}

def action_set_url(url):
    targets = cdp_get_targets()
    for t in targets:
        wsurl = t.get("webSocketDebuggerUrl")
        if not wsurl: continue
        try:
            if websocket:
                ws = websocket.create_connection(wsurl, timeout=3)
                ws.send(json.dumps({"id":1,"method":"Page.navigate","params":{"url":url}}))
                ws.close()
                return _ok()
        except Exception: pass
    return {"ok": False, "error": "navigate-failed"}

def action_restart_chromium():
    run("sudo systemctl restart kiosk.service", timeout=20)
    return _ok()

def action_reboot():
    run("sudo /sbin/reboot", timeout=2)
    return _ok()

def action_cec_standby():
    if shutil.which("cec-ctl"):
        run("cec-ctl --to 0 --standby")
    elif shutil.which("cec-client"):
        run("echo 'standby 0' | cec-client -s -d 1")
    else:
        return {"ok": False, "error": "cec-not-installed"}
    return _ok()

def action_cec_on():
    if shutil.which("cec-ctl"):
        run("cec-ctl --to 0 --image-view-on")
    elif shutil.which("cec-client"):
        run("echo 'on 0' | cec-client -s -d 1")
    else:
        return {"ok": False, "error": "cec-not-installed"}
    return _ok()

COMMANDS = {
    "refresh": lambda args: action_refresh(),
    "set_url": lambda args: action_set_url(args.get("url","")),
    "restart_chromium": lambda args: action_restart_chromium(),
    "reboot": lambda args: action_reboot(),
    "cec_off": lambda args: action_cec_standby(),
    "cec_on": lambda args: action_cec_on(),
}

# ======= LOCAL API =======
app = Flask(__name__)

def check_auth(req):
    tok = req.headers.get("X-Auth")
    return tok == SHARED_TOKEN

@app.route("/api/agent/health")
def api_health():
    if not check_auth(request): return ("forbidden", 403)
    return jsonify(get_health())

@app.route("/api/agent/screenshot")
def api_shot():
    if not check_auth(request): return ("forbidden", 403)
    png = grab_screenshot()
    if png is None: return ("no screenshot", 503)
    b64 = base64.b64encode(png).decode("ascii")
    return jsonify({"png_base64": b64})

@app.route("/api/agent/logs")
def api_logs():
    if not check_auth(request): return ("forbidden", 403)
    return jsonify(tail_logs())

@app.route("/api/agent/command", methods=["POST"])
def api_cmd():
    if not check_auth(request): return ("forbidden", 403)
    data = request.get_json(force=True, silent=True) or {}
    cmd = data.get("cmd")
    fn = COMMANDS.get(cmd)
    if not fn: return jsonify({"ok": False, "error": "unknown-cmd"})
    return jsonify(fn(data))

# ======= PHONE HOME LOOP =======
def phone_home_loop():
    while True:
        try:
            health = get_health()
            logs = tail_logs()
            png = grab_screenshot()
            files = {}
            data = {
                "agent_id": AGENT_ID,
                "auth_token": SHARED_TOKEN,
                "health": json.dumps(health),
                "logs": json.dumps(logs),
            }
            if png:
                files["screenshot"] = ("shot.png", png, "image/png")

            requests.post(f"{SERVER}/api/kiosks/report", data=data, files=files, timeout=15)

            r = requests.get(f"{SERVER}/api/kiosks/{AGENT_ID}/commands/next",
                             headers={"X-Auth": SHARED_TOKEN}, timeout=10)
            if r.status_code == 200:
                payload = r.json()
                if payload.get("cmd"):
                    fn = COMMANDS.get(payload["cmd"])
                    if fn:
                        result = fn(payload.get("args", {}))
                        requests.post(f"{SERVER}/api/kiosks/{AGENT_ID}/commands/ack",
                                      json={"result": result}, headers={"X-Auth": SHARED_TOKEN}, timeout=10)
        except Exception as e:
            pass
        time.sleep(INTERVAL_SEC)

def main():
    t = threading.Thread(target=phone_home_loop, daemon=True)
    t.start()
    app.run(host="127.0.0.1", port=LISTEN_PORT)

if __name__ == "__main__":
    main()
EOF

sudo chown display:display /opt/kiosk-agent/kiosk_agent.py
sudo chmod +x /opt/kiosk-agent/kiosk_agent.py
```

### 6. Create Kiosk Agent Service

```bash
sudo tee /etc/systemd/system/kiosk-agent.service << 'EOF'
[Unit]
Description=Kiosk Agent (health, screenshot, control)
After=network-online.target
Wants=network-online.target
[Service]
User=display
Group=display
Environment=XDG_RUNTIME_DIR=/run/user/1000
Environment=WAYLAND_DISPLAY=wayland-0
Environment=KIOSK_AGENT_ID=%H
Environment=KIOSK_SERVER_URL=http://*************:5000
Environment=KIOSK_SHARED_TOKEN=F1n@lC0untd0wn
Environment=KIOSK_REPORT_INTERVAL=180
Environment=KIOSK_CDP_PORT=9222
ExecStart=/opt/kiosk-agent/venv/bin/python /opt/kiosk-agent/kiosk_agent.py
Restart=always
RestartSec=3
NoNewPrivileges=true
AmbientCapabilities=
CapabilityBoundingSet=
PrivateTmp=true
ProtectSystem=full
ProtectHome=true
ProtectKernelTunables=true
ProtectControlGroups=true
LockPersonality=true
[Install]
WantedBy=multi-user.target
EOF
```

### 7. Enable and Start Services

```bash
# Reload systemd
sudo systemctl daemon-reload

# Enable services to start on boot
sudo <NAME_EMAIL>
sudo systemctl enable kiosk.service  
sudo systemctl enable kiosk-agent.service

# Start services in order
sudo <NAME_EMAIL>
sleep 3
sudo systemctl start kiosk.service
sudo systemctl start kiosk-agent.service
```

### 8. Configure Network (Optional Static IP)

```bash
# For static IP configuration:
sudo nmcli connection modify preconfigured ipv4.addresses *************/24
sudo nmcli connection modify preconfigured ipv4.gateway ***********
sudo nmcli connection modify preconfigured ipv4.dns "********** ********** **********"
sudo nmcli connection modify preconfigured ipv4.method manual
sudo nmcli connection modify preconfigured ipv4.dns-search "BUS.local"
sudo nmcli connection up preconfigured
```

### 9. Verification Commands

```bash
# Check all services are running
sudo <NAME_EMAIL> kiosk.service kiosk-agent.service

# Check Wayland socket exists
ls -l /run/user/1000/wayland-*

# Check Chromium is running and debugging port is listening
ps aux | grep chromium-browser
netstat -tlnp | grep 9222

# Test agent API locally
curl -H "X-Auth: F1n@lC0untd0wn" http://127.0.0.1:7070/api/agent/health
```

### 10. Customization for Different Stations

To setup additional stations, only change the URL in step 4:

```bash
# Station 1: Pack/1/1
# Station 2: Pack/1/2  
# Station 3: Pack/1/3
# etc.
```

This guide includes all required packages and handles the common issues (missing binaries, TTY conflicts, profile locking, socket timing) that we encountered during troubleshooting.