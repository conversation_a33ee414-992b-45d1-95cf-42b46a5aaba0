<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Connectivity Logs</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e1e2e 0%, #2d2d44 100%);
            color: #e0e0e0;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .controls {
            margin-bottom: 20px;
        }

        .toggle-btn {
            background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin-right: 10px;
        }

        .toggle-btn.active {
            background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th,
        td {
            padding: 8px 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .log-online {
            background: rgba(76, 175, 80, 0.15);
        }

        .log-offline {
            background: rgba(244, 67, 54, 0.15);
        }

        th {
            background: rgba(255, 255, 255, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            text-decoration: none;
        }

        .status-detail {
            font-size: 0.9em;
            color: #b0b0b0;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>Connectivity Logs</h1>
        <div class="controls">
            <a class="btn" href="/">← Back</a>
            <button class="toggle-btn {{ 'active' if log_type == 'gateway' else '' }}"
                onclick="switchLogType('gateway')">Gateway Logs</button>
            <button class="toggle-btn {{ 'active' if log_type == 'pi' else '' }}" onclick="switchLogType('pi')">Pi
                Logs</button>
        </div>
        <table>
            <tr>
                <th>{{ 'Gateway' if log_type == 'gateway' else 'Pi' }}</th>
                <th>Status</th>
                <th>Time</th>
                <th>Duration</th>
                <th>Details</th>
            </tr>
            {% for log in logs %}
            <tr class="log-{{ log.status }}">
                <td>{{ log.gateway }}</td>
                <td>{{ log.status }}</td>
                <td>{{ log.timestamp }}</td>
                <td>{{ log.duration }}</td>
                <td class="status-detail">{{ log.status_detail }}</td>
            </tr>
            {% endfor %}
        </table>
    </div>

    <script>
        function switchLogType(type) {
            window.location.href = '/logs?type=' + type;
        }
    </script>
</body>

</html>