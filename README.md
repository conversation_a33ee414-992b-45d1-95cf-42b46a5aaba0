# GatewayCheck API Guide

Gateway Checker for OT team and for <PERSON> to dig into Flask applications as well as API routes & interactions.

## Prerequisites

- Python 3.11+
- Dependencies from `requirements.txt`
- Running the app (`python app.py`)

## Base URL

When the app is running locally, the base URL is:

```
http://localhost:5000
```

Replace `localhost` with your host if accessing remotely.

## Using Postman

The following steps outline how to interact with the API using Postman.

### 1. Retrieve Gateway Status

- **Method**: `GET`
- **URL**: `/api/status`
- **Description**: Returns JSON containing the current status of all gateways.
- **Steps**:
  1. Open Postman and create a new request.
  2. Set the method to `GET`.
  3. Enter `http://localhost:5000/api/status` as the request URL.
  4. Click **Send** to retrieve the status data.

### 2. Add a New Gateway

- **Method**: `POST`
- **URL**: `/api/gateways`
- **Payload**: JSON body containing at least `name`, `host`, `port`, and `type`.
  Optional fields include `web_port`, `ssl`, `order`, and `category`.
- **Steps**:
  1. Create a new `POST` request in Postman.
  2. Use `http://localhost:5000/api/gateways` as the URL.
  3. In the **Body** tab, choose **raw** and set **JSON**.
  4. Provide payload data, for example:
     ```json
     {
       "name": "My Gateway",
       "host": "************",
       "port": 8060,
       "type": "Custom"
     }
     ```
  5. Click **Send**. A successful response returns the details of the added gateway.

### 3. Remove a Gateway

- **Method**: `DELETE`
- **URL**: `/api/gateways/<gateway_name>`
- **Description**: Removes a gateway by name.
- **Steps**:
  1. Create a `DELETE` request.
  2. Set the URL to `http://localhost:5000/api/gateways/<gateway_name>`,
     replacing `<gateway_name>` with the actual name.
  3. Click **Send** to remove the gateway.

### 4. Force a Status Refresh

- **Method**: `GET`
- **URL**: `/api/refresh`
- **Description**: Triggers an immediate status check for all gateways.
- **Steps**:
  1. Create a `GET` request with URL `http://localhost:5000/api/refresh`.
  2. Click **Send`. The response confirms that the refresh was initiated.

## Example Workflow

1. Start the Flask application:
   ```bash
   python app.py
   ```
2. In Postman, call `/api/status` to view the initial status.
3. Use `/api/gateways` to add a new gateway if desired.
4. Call `/api/refresh` to update statuses immediately.
5. Remove a gateway with `/api/gateways/<gateway_name>` when finished.

This README provides a quick reference for interacting with the GatewayCheck API
using Postman.


## Pi of Sauron

A lightweight Raspberry Pi manager lives at `/pisauron`. Devices running the
`pi_agent.py` script report their health and a screenshot back to GatewayCheck.

### Running the agent

```bash
python pi_agent.py
```

Set the `KIOSK_SHARED_TOKEN` environment variable on both the server and agent
to authenticate the reports.


## Setup Pi of Sauron - Disregard below, have been trying to adapt to X11 version - original design for no-desktop raspi

### New Headless Version

add these 2 to chrome flags in service

--remote-debugging-port=9222
--remote-allow-origins=*


sudo systemctl daemon-reload
sudo systemctl restart kiosk.service

check

curl -s http://127.0.0.1:9222/json | head

install agent

sudo mkdir -p /opt/kiosk-agent
sudo chown display:display /opt/kiosk-agent
sudo apt update
sudo apt install -y python3-venv python3-dev build-essential

# as 'display'
sudo -u display python3 -m venv /opt/kiosk-agent/venv
sudo -u display /opt/kiosk-agent/venv/bin/pip install --upgrade pip
sudo -u display /opt/kiosk-agent/venv/bin/pip install flask requests websocket-client

add script

```bash
sudo -u display tee /opt/kiosk-agent/kiosk_agent.py >/dev/null <<'EOF'
#!/usr/bin/env python3
import os, sys, json, time, base64, socket, subprocess, threading, requests, platform, shutil
from datetime import datetime
from flask import Flask, request, jsonify

# ======= CONFIG =======
AGENT_ID = os.environ.get("KIOSK_AGENT_ID", socket.gethostname())
SERVER = os.environ.get("KIOSK_SERVER_URL", "http://*************:5000")
SHARED_TOKEN = os.environ.get("KIOSK_SHARED_TOKEN", "F1n@lC0untd0wn")   # same token on server 
INTERVAL_SEC = int(os.environ.get("KIOSK_REPORT_INTERVAL", "180"))

# Chromium remote debugging
CDP_PORT = int(os.environ.get("KIOSK_CDP_PORT", "9222"))
CHROMIUM_BIN = os.environ.get("KIOSK_CHROMIUM_BIN", "/usr/bin/chromium")

# Services and logs to sample
SERVICE_NAMES = os.environ.get("KIOSK_SERVICE_NAMES", "kiosk.service;weston.service").split(";")
LOG_LINES = int(os.environ.get("KIOSK_LOG_LINES", "200"))

# Local API
LISTEN_PORT = int(os.environ.get("KIOSK_LOCAL_PORT", "7070"))

# ======= UTIL =======
def run(cmd, timeout=10, user_env=None):
    try:
        return subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout, env=user_env)
    except Exception as e:
        return subprocess.CompletedProcess(cmd, 1, "", str(e))

def is_wayland():
    # Weston kiosk uses Wayland – presence of WAYLAND_DISPLAY in weston session
    return True

def get_wifi_info():
    # Works on both NM and wpa_supplicant based systems (best-effort)
    out = run("iw dev | awk '$1==\"Interface\"{print $2}'").stdout.strip()
    if not out:
        return {}
    iface = out.splitlines()[0]
    ssid = run(f"iwgetid {iface} -r").stdout.strip()
    rssi = run(f"iw dev {iface} link | awk '/signal/ {{print $2}}'").stdout.strip()
    ip = run(f"ip -4 addr show {iface} | awk '/inet /{{print $2}}' | cut -d/ -f1").stdout.strip()
    return {"iface": iface, "ssid": ssid, "rssi_dbm": rssi, "ip": ip}

def get_cpu_temp_c():
    paths = ["/sys/class/thermal/thermal_zone0/temp", "/sys/class/thermal/thermal_zone1/temp"]
    for p in paths:
        if os.path.exists(p):
            try:
                v = int(open(p).read().strip())
                return round(v/1000.0, 1)
            except:
                pass
    return None

def get_health():
    disk = shutil.disk_usage("/")
    mem = run("free -m | awk '/Mem:/ {print $2\",\"$3\",\"$7}'").stdout.strip().split(",")
    uptime = run("awk '{print int($1)}' /proc/uptime").stdout.strip()
    health = {
        "agent_id": AGENT_ID,
        "ts": datetime.utcnow().isoformat()+"Z",
        "os": platform.platform(),
        "kernel": platform.release(),
        "uptime_sec": int(uptime) if uptime.isdigit() else None,
        "cpu_temp_c": get_cpu_temp_c(),
        "disk_total_gb": round(disk.total/1024/1024/1024,1),
        "disk_used_gb": round((disk.total-disk.free)/1024/1024/1024,1),
        "mem_total_mb": int(mem[0]) if len(mem)>=1 and mem[0].isdigit() else None,
        "mem_used_mb": int(mem[1]) if len(mem)>=2 and mem[1].isdigit() else None,
        "mem_free_mb": int(mem[2]) if len(mem)>=3 and mem[2].isdigit() else None,
        "net": get_wifi_info(),
        "cec": detect_cec(),
        "display_mode": "wayland" if is_wayland() else "x11",
    }
    # service states
    svc = {}
    for s in SERVICE_NAMES:
        s = s.strip()
        if not s: continue
        status = run(f"systemctl is-active {s}").stdout.strip()
        svc[s] = status or "unknown"
    health["services"] = svc
    return health

def tail_logs():
    logs = {}
    for s in SERVICE_NAMES:
        s = s.strip()
        if not s: continue
        logs[s] = run(f"journalctl -u {s} -n {LOG_LINES} --no-pager").stdout[-40000:]
    logs["kernel"] = run(f"dmesg | tail -n {LOG_LINES}").stdout
    return logs

def detect_cec():
    out = run("which cec-ctl").returncode == 0 or run("which cec-client").returncode == 0
    return {"available": bool(out)}

# ======= SCREENSHOT (via CDP) =======
import http.client, websocket, json as js

def cdp_get_targets():
    try:
        conn = http.client.HTTPConnection("127.0.0.1", CDP_PORT, timeout=2)
        conn.request("GET", "/json")
        r = conn.getresponse()
        data = r.read()
        conn.close()
        arr = json.loads(data.decode("utf-8"))
        arr = [t for t in arr if t.get("type") == "page"]
        arr.sort(key=lambda t: (("kiosk" not in (t.get("title") or "").lower()), t.get("id")))
        return arr
    except Exception:
        return []

def cdp_capture_png(target_ws_url):
    ws = websocket.create_connection(target_ws_url, timeout=3)
    msg_id = 0
    def send(m):
        nonlocal msg_id
        msg_id += 1
        m["id"] = msg_id
        ws.send(js.dumps(m))
        return msg_id
    def recv_until(idwant):
        while True:
            resp = js.loads(ws.recv())
            if resp.get("id") == idwant:
                return resp

    send({"method":"Page.enable"})
    rid = send({"method":"Page.captureScreenshot","params":{"format":"png","fromSurface":True}})
    resp = recv_until(rid)
    ws.close()
    if "result" in resp and "data" in resp["result"]:
        return base64.b64decode(resp["result"]["data"])
    return None

def grab_screenshot():
    targets = cdp_get_targets()
    for t in targets:
        wsurl = t.get("webSocketDebuggerUrl")
        if not wsurl:
            continue
        try:
            png = cdp_capture_png(wsurl)
            if png: return png
        except Exception:
            continue
    return None

# ======= COMMAND EXECUTION =======
def _ok():
    return {"ok": True, "ts": datetime.utcnow().isoformat()+"Z"}

def action_refresh():
    targets = cdp_get_targets()
    for t in targets:
        wsurl = t.get("webSocketDebuggerUrl")
        if not wsurl: continue
        try:
            ws = websocket.create_connection(wsurl, timeout=3)
            ws.send(js.dumps({"id":1,"method":"Page.reload","params":{"ignoreCache":True}}))
            ws.close()
            return _ok()
        except Exception: pass
    return {"ok": False, "error": "no-devtools-target"}

def action_set_url(url):
    targets = cdp_get_targets()
    for t in targets:
        wsurl = t.get("webSocketDebuggerUrl")
        if not wsurl: continue
        try:
            ws = websocket.create_connection(wsurl, timeout=3)
            ws.send(js.dumps({"id":1,"method":"Page.navigate","params":{"url":url}}))
            ws.close()
            return _ok()
        except Exception: pass
    return {"ok": False, "error": "navigate-failed"}

def action_restart_chromium():
    run("sudo /usr/local/bin/kiosk-restart.sh", timeout=20)
    return _ok()

def action_reboot():
    run("sudo /sbin/reboot", timeout=2)
    return _ok()

def action_cec_standby():
    if shutil.which("cec-ctl"):
        run("cec-ctl --to 0 --standby")
    elif shutil.which("cec-client"):
        run("echo 'standby 0' | cec-client -s -d 1")
    else:
        return {"ok": False, "error": "cec-not-installed"}
    return _ok()

def action_cec_on():
    if shutil.which("cec-ctl"):
        run("cec-ctl --to 0 --image-view-on")
    elif shutil.which("cec-client"):
        run("echo 'on 0' | cec-client -s -d 1")
    else:
        return {"ok": False, "error": "cec-not-installed"}
    return _ok()

COMMANDS = {
    "refresh": lambda args: action_refresh(),
    "set_url": lambda args: action_set_url(args.get("url","")),
    "restart_chromium": lambda args: action_restart_chromium(),
    "reboot": lambda args: action_reboot(),
    "cec_off": lambda args: action_cec_standby(),
    "cec_on": lambda args: action_cec_on(),
}

# ======= LOCAL API (loopback) =======
app = Flask(__name__)

def check_auth(req):
    tok = req.headers.get("X-Auth")
    return tok == SHARED_TOKEN

@app.route("/api/agent/health")
def api_health():
    if not check_auth(request): return ("forbidden", 403)
    return jsonify(get_health())

@app.route("/api/agent/screenshot")
def api_shot():
    if not check_auth(request): return ("forbidden", 403)
    png = grab_screenshot()
    if png is None: return ("no screenshot", 503)
    b64 = base64.b64encode(png).decode("ascii")
    return jsonify({"png_base64": b64})

@app.route("/api/agent/logs")
def api_logs():
    if not check_auth(request): return ("forbidden", 403)
    return jsonify(tail_logs())

@app.route("/api/agent/command", methods=["POST"])
def api_cmd():
    if not check_auth(request): return ("forbidden", 403)
    data = request.get_json(force=True, silent=True) or {}
    cmd = data.get("cmd")
    fn = COMMANDS.get(cmd)
    if not fn: return jsonify({"ok": False, "error": "unknown-cmd"})
    return jsonify(fn(data))

# ======= PHONE HOME LOOP =======
def phone_home_loop():
    while True:
        try:
            health = get_health()
            logs = tail_logs()
            png = grab_screenshot()
            files = {}
            data = {
                "agent_id": AGENT_ID,
                "auth_token": SHARED_TOKEN,
                "health": json.dumps(health),
                "logs": json.dumps(logs),
            }
            if png:
                files["screenshot"] = ("shot.png", png, "image/png")

            requests.post(f"{SERVER}/api/kiosks/report", data=data, files=files, timeout=15)

            r = requests.get(f"{SERVER}/api/kiosks/{AGENT_ID}/commands/next",
                             headers={"X-Auth": SHARED_TOKEN}, timeout=10)
            if r.status_code == 200:
                payload = r.json()
                if payload.get("cmd"):
                    fn = COMMANDS.get(payload["cmd"])
                    if fn:
                        result = fn(payload.get("args", {}))
                        requests.post(f"{SERVER}/api/kiosks/{AGENT_ID}/commands/ack",
                                      json={"result": result}, headers={"X-Auth": SHARED_TOKEN}, timeout=10)
        except Exception as e:
            pass
        time.sleep(INTERVAL_SEC)

def main():
    t = threading.Thread(target=phone_home_loop, daemon=True)
    t.start()
    app.run(host="127.0.0.1", port=LISTEN_PORT)

if __name__ == "__main__":
    main() 
EOF sudo 
```

sudo tee /usr/local/bin/kiosk-restart.sh >/dev/null <<'EOF'
#!/bin/sh
exec /bin/systemctl restart kiosk.service
EOF
sudo chmod +x /usr/local/bin/kiosk-restart.sh


sudo tee /etc/sudoers.d/kiosk-agent >/dev/null <<'EOF'
display ALL=(root) NOPASSWD: /bin/systemctl restart kiosk.service, /sbin/reboot
EOF


Create /etc/systemd/system/kiosk-agent.service

[Unit]
Description=Kiosk Agent (health, screenshot, control)
After=network-online.target <EMAIL> kiosk.service
Wants=network-online.target

[Service]
User=display
Group=display
Environment=KIOSK_AGENT_ID=%H
Environment=KIOSK_SERVER_URL=http://*************:5000
Environment=KIOSK_SHARED_TOKEN=change-me
Environment=KIOSK_REPORT_INTERVAL=180
Environment=KIOSK_CDP_PORT=9222
Environment=KIOSK_CHROMIUM_BIN=/usr/bin/chromium
Environment=KIOSK_SERVICE_NAMES=kiosk.service;<EMAIL>
Environment=KIOSK_LOG_LINES=200
Environment=KIOSK_LOCAL_PORT=7070

# Ensure Wayland/XDG paths exist for this user
Environment=XDG_RUNTIME_DIR=/run/user/1000
Environment=WAYLAND_DISPLAY=wayland-0

WorkingDirectory=/opt/kiosk-agent
ExecStart=/opt/kiosk-agent/venv/bin/python /opt/kiosk-agent/kiosk_agent.py
Restart=always
RestartSec=5
NoNewPrivileges=true
AmbientCapabilities=
CapabilityBoundingSet=
PrivateTmp=true
ProtectSystem=full
ProtectHome=true
ProtectKernelTunables=true
ProtectControlGroups=true
LockPersonality=true

[Install]
WantedBy=multi-user.target


sudo systemctl daemon-reload
sudo systemctl enable --now kiosk-agent.service
sudo systemctl status kiosk-agent.service --no-pager
