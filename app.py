from flask import Flask, render_template, jsonify, request
import os
import socket
import requests
import threading
import time
from datetime import datetime, timedelta
import json
from urllib.parse import urlparse
import ssl
import urllib3
import subprocess
import platform
import sqlite3
import base64

# Disable SSL warnings for self-signed certificates
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

app = Flask(__name__)

# Shared token for kiosk agents - This should normally be set to an env variable but this is for demonstration
KIOSK_SHARED_TOKEN = os.environ.get("KIOSK_SHARED_TOKEN", "F1n@lC0untd0wn")

# Storage for kiosk reports and pending commands
kiosk_reports = {}
kiosk_commands = {}

pi_status = {}
pi_last_status_info = {}

# MQTT credentials for broker authentication
MQTT_USERNAME = "visualfactory"
MQTT_PASSWORD = "Pr0terr@"

HEXMES_WEATHER_ENDPOINT = "http://**********:8000/api/environmental/weather/update"
WEATHER_PUSH_INTERVAL = 10  # 10fast 10 furious
WEATHER_API_KEY = "640e721f57ae47125d636544b1bcf297"
# WEATHER_CACHE_DURATION = 300
GREER_LAT = 34.9262
GREER_LON = -82.2270

weather_cache = {
    "data": None,
    "timestamp": None,
    "cache_duration": 10,
}

# Gateway configuration - easily add new gateways here
GATEWAYS = {
    "Production Gateways": [
        {
            "name": "Ignition-gr-ot-igngw-02p",
            "host": "**********",
            "port": 8060,
            "web_port": 8088,
            "type": "Local",
            "ssl": False,
            "order": 2,
        },
        {
            "name": "Ignition MES",
            "host": "ignitionmes1.mes-greer.proterra.com",
            "port": 8060,
            "web_port": 8043,
            "type": "Primary",
            "ssl": True,
            "order": 1,
        },
        {
            "name": "Ignition-c1-ign-prd-01",
            "host": "***********",
            "port": 8060,
            "web_port": 8088,
            "type": "C1",
            "ssl": False,
            "order": 9,
        },
        {
            "name": "Ignition Scada 2",
            "host": "ignitionscada2.scada-greer.proterra.com",
            "port": 8060,
            "web_port": 8043,
            "type": "SCADA",
            "ssl": True,
            "order": 7,
        },
        {
            "name": "Ignition Backend 1",
            "host": "ignitionbackend1.mes.proterra.com",
            "port": 8060,
            "web_port": 8043,
            "type": "SCADA",
            "ssl": True,
            "order": 3,
        },
        {
            "name": "Ignition Backend 2",
            "host": "ignitionbackend2.mes.proterra.com",
            "port": 8060,
            "web_port": 8043,
            "type": "SCADA",
            "ssl": True,
            "order": 4,
        },
        {
            "name": "Ignition Backend 2b",
            "host": "ignitionscada2b.scada-greer.proterra.com",
            "port": 8060,
            "web_port": 8043,
            "type": "SCADA",
            "ssl": True,
            "order": 5,
        },
    ],
    "Development/Other": [
        {
            "name": "Chariot1 MQTT",
            "host": "Chariot1.mes.proterra.com",
            "port": 1883,
            "web_port": None,
            "type": "MQTT",
            "order": 6,
        },
        {
            "name": "VF Gateway MQTT",
            "host": "VF-Gateway-01",
            "port": 1883,
            "web_port": None,
            "type": "MQTT",
            "order": 9,
        },
        {
            "name": "Ignition MES Dev",
            "host": "ignitionmes1.mes-dev-greer.proterra.com",
            "port": 8060,
            "web_port": 8043,
            "type": "Development",
            "ssl": True,
            "order": 8,
        },
    ],
}

# Global status storage
gateway_status = {}

# Lock to synchronize access to the GATEWAYS structure
gateways_lock = threading.Lock()

# SQLite setup for logging status changes
db_conn = sqlite3.connect("gateway_logs.db", check_same_thread=False)
db_cursor = db_conn.cursor()
db_cursor.execute(
    """
    CREATE TABLE IF NOT EXISTS logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        gateway_name TEXT,
        status TEXT,
        timestamp TEXT,
        duration REAL,
        log_type TEXT DEFAULT 'gateway',
        status_detail TEXT
    )
    """
)
db_conn.commit()
try:
    db_cursor.execute("ALTER TABLE logs ADD COLUMN log_type TEXT DEFAULT 'gateway'")
    db_cursor.execute("ALTER TABLE logs ADD COLUMN status_detail TEXT")
    db_conn.commit()
except sqlite3.OperationalError:
    pass
# Lock to prevent concurrent access to the SQLite cursor and status tracking
db_lock = threading.Lock()

# Track last known status and time for each gateway
last_status_info = {}


def ping_host(host, timeout=5):
    """Ping host to check if it's reachable"""
    try:
        # Determine ping command based on OS
        if platform.system().lower() == "windows":
            cmd = ["ping", "-n", "1", "-w", str(timeout * 1000), host]
        else:
            cmd = ["ping", "-c", "1", "-W", str(timeout), host]

        result = subprocess.run(
            cmd, capture_output=True, text=True, timeout=timeout + 2
        )
        return result.returncode == 0
    except Exception as e:
        print(f"Ping error for {host}: {e}")
        return False


def check_port_connectivity(host, port, timeout=5):
    """Check if host:port is reachable via TCP"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        print(f"Port check error for {host}:{port}: {e}")
        return False


def check_mqtt_connectivity(host, port, username=None, password=None, timeout=5):
    """Attempt an MQTT handshake using paho-mqtt"""
    try:
        import paho.mqtt.client as mqtt

        connected = False

        def on_connect(client, userdata, flags, rc):
            nonlocal connected
            if rc == 0:
                connected = True

        client = mqtt.Client()
        if username:
            client.username_pw_set(username, password)
        client.on_connect = on_connect

        # Initiate connection and start network loop
        client.connect(host, port, keepalive=60)
        client.loop_start()

        start_time = time.time()
        while time.time() - start_time < timeout:
            if connected:
                break
            time.sleep(0.1)

        client.disconnect()
        client.loop_stop()

        return connected
    except Exception as e:
        print(f"MQTT handshake error for {host}:{port}: {e}")
        return False


def check_ignition_web_service(host, web_port, ssl_enabled=False, timeout=10):
    """Check if Ignition web service is responding"""
    try:
        protocol = "https" if ssl_enabled else "http"

        # Try multiple endpoints
        endpoints = [
            f"{protocol}://{host}:{web_port}/system/gateway/info",
            f"{protocol}://{host}:{web_port}/main/system/gateway",
            f"{protocol}://{host}:{web_port}/",
        ]

        for url in endpoints:
            try:
                response = requests.get(
                    url,
                    timeout=timeout,
                    verify=False,  # Skip SSL verification for self-signed certs
                    allow_redirects=True,
                )

                if response.status_code in [
                    200,
                    401,
                    403,
                ]:  # 401/403 means service is running but needs auth
                    return True, f"Service Running(HTTP {response.status_code})"

            except requests.exceptions.Timeout:
                continue
            except requests.exceptions.ConnectionError:
                continue
            except Exception:
                continue

        # If all endpoints failed, try simple port connectivity
        if check_port_connectivity(host, web_port, timeout=5):
            return True, "Web Port Open"
        else:
            return False, "Web Service Unreachable"

    except Exception as e:
        return False, f"Error: {str(e)[:30]}"


def monitor_gateway(gateway_info):
    """Monitor a single gateway with improved logging"""
    name = gateway_info["name"]
    host = gateway_info["host"]
    port = gateway_info["port"]
    web_port = gateway_info.get("web_port")
    gateway_type = gateway_info["type"]
    ssl_enabled = gateway_info.get("ssl", False)

    # Check host connectivity via ping
    host_online = ping_host(host)

    # Check service status
    service_status = "N/A"
    service_running = None

    if gateway_type == "MQTT":
        service_running = check_mqtt_connectivity(
            host, port, username=MQTT_USERNAME, password=MQTT_PASSWORD, timeout=5
        )
        service_status = (
            "MQTT Service Running" if service_running else "MQTT Service Down"
        )
    elif web_port and host_online:
        service_running, service_status = check_ignition_web_service(
            host, web_port, ssl_enabled
        )
    elif web_port:
        service_running, service_status = check_ignition_web_service(
            host, web_port, ssl_enabled
        )
        if service_running:
            host_online = True
    else:
        service_running = check_port_connectivity(host, port, timeout=5)
        service_status = (
            "Service Port Open" if service_running else "Service Port Closed"
        )

    # Determine overall status with more detail
    if gateway_type == "MQTT":
        overall_status = "online" if service_running else "offline"
        status_detail = f"Host: {'✓' if host_online else '✗'}, MQTT: {'✓' if service_running else '✗'}"
    else:
        overall_status = "online" if service_running else "offline"
        status_detail = f"Host: {'✓' if host_online else '✗'}, Service: {'✓' if service_running else '✗'}"

    # Update global status
    gateway_status[name] = {
        "name": name,
        "host": host,
        "port": port,
        "web_port": web_port,
        "type": gateway_type,
        "host_online": host_online,
        "service_running": service_running,
        "service_status": service_status,
        "last_check": datetime.now().strftime("%H:%M:%S"),
        "overall_status": overall_status,
        "status_detail": status_detail,
        "order": gateway_info.get("order", 999),
    }

    # Log any status change to SQLite with better detail
    with db_lock:
        now = datetime.now()
        prev = last_status_info.get(name)
        if prev is None:
            db_cursor.execute(
                "INSERT INTO logs (gateway_name, status, timestamp, duration, log_type, status_detail) VALUES (?, ?, ?, ?, ?, ?)",
                (name, overall_status, now.isoformat(), None, "gateway", status_detail),
            )
            db_conn.commit()
            last_status_info[name] = {
                "status": overall_status,
                "status_detail": status_detail,
                "time": now,
                "log_id": db_cursor.lastrowid,
            }
        else:
            # Only log if status OR detail changes
            if overall_status != prev["status"] or status_detail != prev.get(
                "status_detail", ""
            ):
                duration = (now - prev["time"]).total_seconds()
                db_cursor.execute(
                    "UPDATE logs SET duration=? WHERE id=?",
                    (duration, prev["log_id"]),
                )
                db_cursor.execute(
                    "INSERT INTO logs (gateway_name, status, timestamp, duration, log_type, status_detail) VALUES (?, ?, ?, ?, ?, ?)",
                    (
                        name,
                        overall_status,
                        now.isoformat(),
                        None,
                        "gateway",
                        status_detail,
                    ),
                )
                db_conn.commit()
                last_status_info[name] = {
                    "status": overall_status,
                    "status_detail": status_detail,
                    "time": now,
                    "log_id": db_cursor.lastrowid,
                }


def fetch_weather_from_api():
    """Fetch fresh weather data from OpenWeatherMap API"""
    try:
        if not WEATHER_API_KEY or WEATHER_API_KEY == "your_openweather_api_key_here":
            # Return mock data when no API key is set
            return {
                "temperature_c": 22.5,
                "temperature_f": 72.5,
                "humidity": 65,
                "description": "Partly cloudy",
                "location": "Greer, SC",
                "timestamp": datetime.now().isoformat(),
                "status": "success",
                "mock_data": True,
            }

        # Make request to OpenWeatherMap
        url = f"https://api.openweathermap.org/data/2.5/weather"
        params = {
            "lat": GREER_LAT,
            "lon": GREER_LON,
            "appid": WEATHER_API_KEY,
            "units": "metric",
        }

        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()

        data = response.json()

        temp_c = data["main"]["temp"]
        temp_f = (temp_c * 9 / 5) + 32
        humidity = data["main"]["humidity"]

        weather_data = {
            "temperature_c": round(temp_c, 1),
            "temperature_f": round(temp_f, 1),
            "humidity": humidity,
            "description": data["weather"][0]["description"].title(),
            "location": "Greer, SC",
            "timestamp": datetime.now().isoformat(),
            "status": "success",
            "mock_data": False,
        }

        return weather_data

    except requests.exceptions.Timeout:
        raise Exception("Weather API request timeout")
    except requests.exceptions.RequestException as e:
        raise Exception(f"Weather API request failed: {str(e)}")
    except KeyError as e:
        raise Exception(f"Invalid weather API response format: {str(e)}")
    except Exception as e:
        raise Exception(f"Weather API error: {str(e)}")


def get_cached_weather_data():
    """Get weather data from cache or fetch from API if cache is expired"""
    now = datetime.now()

    # Check if cache is valid (data exists and is not expired)
    if (
        weather_cache["data"]
        and weather_cache["timestamp"]
        and (now - weather_cache["timestamp"]).seconds < weather_cache["cache_duration"]
    ):

        # Add cache info to response
        cached_data = weather_cache["data"].copy()
        cached_data["cached"] = True
        cached_data["cache_age_seconds"] = (now - weather_cache["timestamp"]).seconds
        return cached_data

    try:
        # Fetch fresh data from API
        weather_data = fetch_weather_from_api()

        # Update cache with new data
        weather_cache["data"] = weather_data
        weather_cache["timestamp"] = now

        # Add cache info to response
        weather_data["cached"] = False
        weather_data["cache_age_seconds"] = 0

        print(
            f"Weather data refreshed: {weather_data['temperature_c']}°C, {weather_data['humidity']}%"
        )
        return weather_data

    except Exception as e:
        print(f"Weather API error: {e}")

        # If we have old cached data, return it with error flag
        if weather_cache["data"]:
            error_data = weather_cache["data"].copy()
            error_data["cached"] = True
            error_data["cache_age_seconds"] = (now - weather_cache["timestamp"]).seconds
            error_data["status"] = "error"
            error_data["error_message"] = str(e)
            return error_data

        # No cached data available, return error response
        return {
            "error": True,
            "status": "error",
            "message": str(e),
            "location": "Greer, SC",
            "timestamp": now.isoformat(),
            "cached": False,
        }


def push_weather_to_hexmes():
    """Push weather data to HexMES server"""
    while True:
        try:
            # Get fresh weather data
            weather_data = get_cached_weather_data()

            if not weather_data.get("error"):
                # Push to HexMES server
                response = requests.post(
                    HEXMES_WEATHER_ENDPOINT,
                    json=weather_data,
                    timeout=10,
                    headers={"Content-Type": "application/json"},
                )

                if response.status_code == 200:
                    print(f"Weather data pushed successfully at {datetime.now()}")
                else:
                    print(f"Failed to push weather data: HTTP {response.status_code}")
            else:
                print(
                    f"Skipping push due to weather data error: {weather_data.get('message')}"
                )

        except Exception as e:
            print(f"Error pushing weather data to HexMES: {e}")

        time.sleep(WEATHER_PUSH_INTERVAL)


# Start weather push thread in background
def start_weather_push_service():
    """Start the weather push service"""
    push_thread = threading.Thread(target=push_weather_to_hexmes, daemon=True)
    push_thread.start()
    print("Weather push service started")


def update_pi_status():
    """Update Pi status and log changes"""
    current_time = datetime.now()

    for agent_id, info in kiosk_reports.items():
        # Determine if Pi is online (last report within 5 minutes)
        last_report = datetime.fromisoformat(
            info["timestamp"].replace("Z", "+00:00")
        ).replace(tzinfo=None)
        is_online = (current_time - last_report).total_seconds() < 300  # 5 minutes

        current_status = "online" if is_online else "offline"

        # Update pi_status
        pi_status[agent_id] = {
            "agent_id": agent_id,
            "status": current_status,
            "last_report": info["timestamp"],
            "last_check": current_time.strftime("%H:%M:%S"),
        }

        # Log status changes
        with db_lock:
            prev = pi_last_status_info.get(agent_id)
            if prev is None:
                # First observation
                db_cursor.execute(
                    "INSERT INTO logs (gateway_name, status, timestamp, duration, log_type) VALUES (?, ?, ?, ?, ?)",
                    (agent_id, current_status, current_time.isoformat(), None, "pi"),
                )
                db_conn.commit()
                pi_last_status_info[agent_id] = {
                    "status": current_status,
                    "time": current_time,
                    "log_id": db_cursor.lastrowid,
                }
            else:
                if current_status != prev["status"]:
                    duration = (current_time - prev["time"]).total_seconds()
                    # Update duration for the previous entry
                    db_cursor.execute(
                        "UPDATE logs SET duration=? WHERE id=?",
                        (duration, prev["log_id"]),
                    )
                    # Insert new status entry
                    db_cursor.execute(
                        "INSERT INTO logs (gateway_name, status, timestamp, duration, log_type) VALUES (?, ?, ?, ?, ?)",
                        (
                            agent_id,
                            current_status,
                            current_time.isoformat(),
                            None,
                            "pi",
                        ),
                    )
                    db_conn.commit()
                    pi_last_status_info[agent_id] = {
                        "status": current_status,
                        "time": current_time,
                        "log_id": db_cursor.lastrowid,
                    }


def monitor_all_gateways():
    """Monitor all gateways in parallel"""
    threads = []

    with gateways_lock:
        gateways_copy = {cat: list(gws) for cat, gws in GATEWAYS.items()}

    for category, gateways in gateways_copy.items():
        for gateway in gateways:
            thread = threading.Thread(target=monitor_gateway, args=(gateway,))
            threads.append(thread)
            thread.start()

    # Wait for all threads to complete
    for thread in threads:
        thread.join()


def background_monitor():
    """Background thread to continuously monitor gateways"""
    while True:
        monitor_all_gateways()
        update_pi_status()
        time.sleep(30)  # Check every 30 seconds


@app.route("/")
def index():
    return render_template("index.html")


@app.route("/api/status")
def get_status():
    """API endpoint to get current gateway status"""
    return jsonify(gateway_status)


@app.route("/api/gateways", methods=["POST"])
def add_gateway():
    """Add a new gateway via API"""
    data = request.get_json(silent=True) or {}
    required = ["name", "host", "port", "type"]
    for field in required:
        if field not in data:
            return jsonify({"error": f"Missing field: {field}"}), 400

    category = data.get("category", "Dynamic")

    new_gateway = {
        "name": data["name"],
        "host": data["host"],
        "port": data["port"],
        "web_port": data.get("web_port"),
        "type": data["type"],
        "ssl": data.get("ssl", False),
        "order": data.get("order", 999),
    }

    with gateways_lock:
        for gws in GATEWAYS.values():
            if any(g["name"] == new_gateway["name"] for g in gws):
                return jsonify({"error": "Gateway with that name already exists"}), 400
        if category not in GATEWAYS:
            GATEWAYS[category] = []
        GATEWAYS[category].append(new_gateway)

    # Start monitoring immediately
    monitor_gateway(new_gateway)

    return jsonify({"status": "added", "gateway": new_gateway}), 201


@app.route("/api/gateways/<gateway_name>", methods=["DELETE"])
def remove_gateway(gateway_name):
    """Remove a gateway via API"""
    with gateways_lock:
        for category, gws in GATEWAYS.items():
            for i, gw in enumerate(gws):
                if gw["name"] == gateway_name:
                    del gws[i]
                    gateway_status.pop(gateway_name, None)
                    last_status_info.pop(gateway_name, None)
                    return jsonify({"status": "removed"})

    return jsonify({"error": "Gateway not found"}), 404


@app.route("/logs")
def show_logs():
    """Page to display gateway and Pi status change logs"""
    log_type = request.args.get("type", "gateway")  # default to gateway

    if log_type == "pi":
        cursor = db_conn.execute(
            "SELECT gateway_name, status, timestamp, duration, status_detail FROM logs WHERE log_type='pi' OR log_type IS NULL ORDER BY id DESC LIMIT 100"
        )
    else:
        cursor = db_conn.execute(
            "SELECT gateway_name, status, timestamp, duration, status_detail FROM logs WHERE log_type='gateway' OR log_type IS NULL ORDER BY id DESC LIMIT 100"
        )

    logs = [
        {
            "gateway": row[0],
            "status": row[1],
            "timestamp": row[2],
            "duration": (
                str(timedelta(seconds=int(row[3]))) if row[3] is not None else ""
            ),
            "status_detail": row[4] or "",
        }
        for row in cursor.fetchall()
    ]
    return render_template("logs.html", logs=logs, log_type=log_type)


@app.route("/api/kiosks/<agent_id>/monitor-log")
def kiosk_monitor_log(agent_id):
    """Fetch monitor log from a Pi"""
    if agent_id not in kiosk_reports:
        return jsonify({"error": "Pi not found"}), 404

    try:
        # Get the Pi's IP from its health data
        pi_info = kiosk_reports[agent_id]
        pi_ip = pi_info.get("health", {}).get("net", {}).get("ip")

        if not pi_ip:
            return jsonify({"error": "Pi IP not available"}), 404

        # Fetch the log from the Pi
        response = requests.get(
            f"http://{pi_ip}:7070/api/agent/monitor-log",
            headers={"X-Auth": KIOSK_SHARED_TOKEN},
            timeout=10,
        )

        if response.status_code == 200:
            return response.json()
        else:
            return jsonify({"error": "Failed to fetch log"}), response.status_code

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/api/refresh")
def refresh_status():
    """API endpoint to force refresh all gateways"""
    monitor_all_gateways()
    return jsonify({"status": "refreshed", "timestamp": datetime.now().isoformat()})


def _kiosk_auth(token: str) -> bool:
    return token == KIOSK_SHARED_TOKEN


@app.route("/api/kiosks/report", methods=["POST"])
def kiosk_report():
    token = request.form.get("auth_token")
    if not _kiosk_auth(token):
        return ("forbidden", 403)
    agent_id = request.form.get("agent_id", "unknown")
    health = json.loads(request.form.get("health", "{}"))
    logs = json.loads(request.form.get("logs", "{}"))
    screenshot_b64 = None
    if "screenshot" in request.files:
        screenshot_b64 = base64.b64encode(request.files["screenshot"].read()).decode(
            "ascii"
        )
    # capture the remote IP address (respect X-Forwarded-For if present)
    ip_addr = (
        request.headers.get("X-Forwarded-For", request.remote_addr)
        .split(",")[0]
        .strip()
    )

    kiosk_reports[agent_id] = {
        "health": health,
        "logs": logs,
        "screenshot": screenshot_b64,
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "ip": ip_addr,
    }
    return jsonify({"ok": True})


@app.route("/api/kiosks/status")
def kiosk_status():
    return jsonify(kiosk_reports)


@app.route("/api/kiosks/<agent_id>/commands/next")
def kiosk_command_next(agent_id):
    if not _kiosk_auth(request.headers.get("X-Auth")):
        return ("forbidden", 403)
    cmd = kiosk_commands.pop(agent_id, None)
    if cmd:
        return jsonify(cmd)
    return ("", 204)


@app.route("/api/kiosks/<agent_id>/commands/ack", methods=["POST"])
def kiosk_command_ack(agent_id):
    if not _kiosk_auth(request.headers.get("X-Auth")):
        return ("forbidden", 403)
    data = request.get_json(silent=True) or {}
    kiosk_reports.setdefault(agent_id, {}).update({"last_result": data.get("result")})
    return jsonify({"ok": True})


@app.route("/api/kiosks/<agent_id>/ping")
def kiosk_ping(agent_id):
    """Simple network ping to the kiosk host."""
    info = kiosk_reports.get(agent_id, {})
    host = info.get("ip") or agent_id
    online = ping_host(host)
    return jsonify({"online": bool(online)})


@app.route("/api/kiosks/<agent_id>/command", methods=["POST"])
def kiosk_command(agent_id):
    """Queue a command for a kiosk agent."""
    data = request.get_json(silent=True) or {}
    cmd = data.get("cmd")
    args = data.get("args", {})
    if not cmd:
        return jsonify({"ok": False, "error": "missing-cmd"}), 400
    kiosk_commands[agent_id] = {"cmd": cmd, "args": args}
    return jsonify({"ok": True})


@app.route("/api/kiosks/<agent_id>", methods=["DELETE"])
def kiosk_delete(agent_id):
    """Remove a kiosk from the current dashboard."""
    removed = kiosk_reports.pop(agent_id, None)
    kiosk_commands.pop(agent_id, None)
    return jsonify({"ok": bool(removed)})


@app.route("/api/weather")
def weather_proxy():
    """Weather proxy endpoint for internal servers to fetch weather data"""
    try:
        weather_data = get_cached_weather_data()
        return jsonify(weather_data)
    except Exception as e:
        return (
            jsonify(
                {
                    "error": True,
                    "status": "error",
                    "message": f"Weather proxy error: {str(e)}",
                    "location": "Greer, SC",
                    "timestamp": datetime.now().isoformat(),
                }
            ),
            500,
        )


@app.route("/api/weather/status")
def weather_status():
    """Weather service status and cache information endpoint"""
    now = datetime.now()
    cache_age = 0
    cache_valid = False

    if weather_cache["timestamp"]:
        cache_age = (now - weather_cache["timestamp"]).seconds
        cache_valid = cache_age < weather_cache["cache_duration"]

    return jsonify(
        {
            "service": "weather_proxy",
            "api_key_configured": WEATHER_API_KEY != "your_openweather_api_key_here",
            "cache_duration_seconds": weather_cache["cache_duration"],
            "cache_age_seconds": cache_age,
            "cache_valid": cache_valid,
            "has_cached_data": weather_cache["data"] is not None,
            "last_update": (
                weather_cache["timestamp"].isoformat()
                if weather_cache["timestamp"]
                else None
            ),
            "location": f"Greer, SC ({GREER_LAT}, {GREER_LON})",
        }
    )


@app.route("/api/weather/refresh")
def weather_refresh():
    """Force refresh weather data (bypass cache)"""
    try:
        # Clear cache to force refresh
        weather_cache["data"] = None
        weather_cache["timestamp"] = None

        # Get fresh data
        weather_data = get_cached_weather_data()

        return jsonify(
            {
                "status": "refreshed",
                "data": weather_data,
                "timestamp": datetime.now().isoformat(),
            }
        )
    except Exception as e:
        return (
            jsonify(
                {
                    "error": True,
                    "message": f"Weather refresh failed: {str(e)}",
                    "timestamp": datetime.now().isoformat(),
                }
            ),
            500,
        )


@app.route("/pisauron")
def pisauron_page():
    return render_template("pisauron.html", kiosks=kiosk_reports)


INDEX_HTML = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ignition Gateway Monitor</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #1e1e2e 0%, #2d2d44 100%); color: #e0e0e0; min-height: 100vh; padding: 20px; }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; padding: 20px; background: rgba(255, 255, 255, 0.05); border-radius: 15px; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1); }
        .header h1 { color: #4fc3f7; margin-bottom: 10px; font-size: 2.5em; text-shadow: 0 0 20px rgba(79, 195, 247, 0.3); }
        .header p { color: #b0b0b0; font-size: 1.1em; }
        .controls { display: flex; justify-content: center; gap: 15px; margin-bottom: 30px; }
        .btn { background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%); color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-size: 1em; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3); }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(79, 195, 247, 0.4); }
        .btn:active { transform: translateY(0); }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .gateway-card { background: rgba(255, 255, 255, 0.08); border-radius: 15px; padding: 20px; border: 1px solid rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); transition: all 0.3s ease; position: relative; overflow: hidden; }
        .gateway-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #4fc3f7, #29b6f6); opacity: 0; transition: opacity 0.3s ease; }
        .gateway-card.online::before { background: linear-gradient(90deg, #4caf50, #66bb6a); opacity: 1; }
        .gateway-card.offline::before { background: linear-gradient(90deg, #f44336, #ef5350); opacity: 1; }
        .gateway-card:hover { transform: translateY(-5px); box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3); }
        .gateway-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .gateway-name { font-size: 1.2em; font-weight: 600; color: #4fc3f7; }
        .gateway-type { background: rgba(79, 195, 247, 0.2); color: #4fc3f7; padding: 4px 12px; border-radius: 20px; font-size: 0.8em; font-weight: 500; }
        .status-item { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; padding: 8px; background: rgba(255, 255, 255, 0.03); border-radius: 8px; }
        .status-label { color: #b0b0b0; font-weight: 500; }
        .status-value { font-weight: 600; }
        .status-online { color: #4caf50; }
        .status-offline { color: #f44336; }
        .status-neutral { color: #ff9800; }
        .last-updated { text-align: center; color: #888; font-size: 0.9em; margin-top: 20px; padding: 15px; background: rgba(255, 255, 255, 0.05); border-radius: 10px; }
        .loading { text-align: center; color: #4fc3f7; font-size: 1.2em; padding: 40px; }
        .spinner { border: 3px solid rgba(79, 195, 247, 0.3); border-top: 3px solid #4fc3f7; border-radius: 50%; width: 30px; height: 30px; animation: spin 1s linear infinite; margin: 0 auto 15px; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .summary { display: flex; justify-content: center; gap: 30px; margin-bottom: 30px; flex-wrap: wrap; }
        .summary-item { background: rgba(255, 255, 255, 0.08); padding: 20px; border-radius: 15px; text-align: center; min-width: 120px; border: 1px solid rgba(255, 255, 255, 0.1); }
        .summary-number { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .summary-online .summary-number { color: #4caf50; }
        .summary-offline .summary-number { color: #f44336; }
        .summary-total .summary-number { color: #4fc3f7; }
        @media (max-width: 768px) { .status-grid { grid-template-columns: 1fr; } .summary { gap: 15px; } .controls { flex-direction: column; align-items: center; } }
        #countdownContainer { margin-top: 10px; }
        #refreshingAnimation { display: flex; align-items: center; justify-content: center; gap: 10px; color: #4fc3f7; font-weight: 500; }
        .refresh-spinner { border: 2px solid rgba(79, 195, 247, 0.3); border-top: 2px solid #4fc3f7; border-radius: 50%; width: 16px; height: 16px; animation: refresh-spin 0.8s linear infinite; }
        @keyframes refresh-spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        #countdown { font-weight: bold; color: #4fc3f7; transition: color 0.3s ease; }
        #countdown.low { color: #ff9800; animation: pulse 1s ease-in-out infinite; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.6; } }
        .last-updated { text-align: center; color: #888; font-size: 0.9em; margin-top: 20px; padding: 15px; background: rgba(255, 255, 255, 0.05); border-radius: 10px; border: 1px solid rgba(255, 255, 255, 0.1); }
        .last-updated div:first-child { margin-bottom: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>OT Team Gateway Monitor</h1>
            <p>Monitoring of gateway infrastructure</p>
        </div>

        <div class="controls">
            <button class="btn" onclick="refreshStatus()">🔄 Refresh All</button>
            <button class="btn" onclick="toggleAutoRefresh()">⏱️ Auto Refresh: <span id="autoRefreshStatus">ON</span></button>
            <a class="btn" href="/pisauron">👁️ Pi of Sauron</a>
            <a class="btn" href="/logs">📄 Logs</a>
        </div>

        <div class="summary" id="summary">
            <!-- Summary will be populated by JavaScript -->
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            Checking gateway status...
        </div>

        <div class="status-grid" id="statusGrid" style="display: none;">
            <!-- Gateway cards will be populated by JavaScript -->
        </div>

        <div class="last-updated" id="lastUpdated" style="display: none;">
            <!-- Last updated info will be populated by JavaScript -->
        </div>
    </div>

    <script>
        let autoRefresh = true;
        let refreshInterval;
        let countdownInterval;
        let countdownSeconds = 30;

        function updateStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('statusGrid').style.display = 'grid';
                    document.getElementById('lastUpdated').style.display = 'block';
                    document.getElementById('summary').style.display = 'flex';
                    
                    updateSummary(data);
                    updateGatewayCards(data);
                    updateLastUpdated();
                    
                    hideRefreshingAnimation();
                    if (autoRefresh) {
                        startCountdown();
                    }
                })
                .catch(error => {
                    console.error('Error fetching status:', error);
                    hideRefreshingAnimation();
                    if (autoRefresh) {
                        startCountdown();
                    }
                });
        }

        function updateSummary(data) {
            const gateways = Object.values(data);
            const total = gateways.length;
            const online = gateways.filter(g => g.overall_status === 'online').length;
            const offline = total - online;

            document.getElementById('summary').innerHTML = `
                <div class="summary-item summary-total">
                    <div class="summary-number">${total}</div>
                    <div>Total</div>
                </div>
                <div class="summary-item summary-online">
                    <div class="summary-number">${online}</div>
                    <div>Online</div>
                </div>
                <div class="summary-item summary-offline">
                    <div class="summary-number">${offline}</div>
                    <div>Offline</div>
                </div>
            `;
        }

        function updateGatewayCards(data) {
            const statusGrid = document.getElementById('statusGrid');
            statusGrid.innerHTML = '';

            const sortedGateways = Object.values(data).sort((a, b) => a.order - b.order);

            sortedGateways.forEach(gateway => {
                const card = document.createElement('div');
                card.className = `gateway-card ${gateway.overall_status}`;
                
                const hostStatus = gateway.host_online ? 'online' : 'offline';
                const serviceStatus = gateway.service_running === true ? 'online' : 
                                    gateway.service_running === false ? 'offline' : 'neutral';
                
                let portInfo = `${gateway.host}`;
                if (gateway.web_port) {
                    portInfo += `:${gateway.web_port}`;
                } else {
                    portInfo += `:${gateway.port}`;
                }
                
                card.innerHTML = `
                    <div class="gateway-header">
                        <div class="gateway-name">${gateway.name}</div>
                        <div class="gateway-type">${gateway.type}</div>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Host Ping (${gateway.host})</span>
                        <span class="status-value status-${hostStatus}">
                            ${gateway.host_online ? '🟢 Reachable' : '🔴 Unreachable'}
                        </span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Service (${portInfo})</span>
                        <span class="status-value status-${serviceStatus}">
                            ${gateway.service_status}
                        </span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Last Check</span>
                        <span class="status-value">${gateway.last_check}</span>
                    </div>
                `;
                
                statusGrid.appendChild(card);
            });
        }

        function updateLastUpdated() {
            const now = new Date();
            const lastUpdatedElement = document.getElementById('lastUpdated');
            
            if (autoRefresh) {
                lastUpdatedElement.innerHTML = `
                    <div>Last updated: ${now.toLocaleTimeString()}</div>
                    <div id="countdownContainer">
                        <span id="countdownText">Next refresh in <span id="countdown">${countdownSeconds}</span> seconds</span>
                        <div id="refreshingAnimation" style="display: none;">
                            <div class="refresh-spinner"></div>
                            <span>Refreshing...</span>
                        </div>
                    </div>
                `;
            } else {
                lastUpdatedElement.innerHTML = `Last updated: ${now.toLocaleTimeString()} | Auto refresh is OFF`;
            }
        }

        function startCountdown() {
            clearInterval(countdownInterval);
            countdownSeconds = 30;
            
            const countdownElement = document.getElementById('countdown');
            if (countdownElement) {
                countdownElement.textContent = countdownSeconds;
                countdownElement.classList.remove('low');
            }
            
            countdownInterval = setInterval(() => {
                countdownSeconds--;
                const countdownEl = document.getElementById('countdown');
                if (countdownEl) {
                    countdownEl.textContent = countdownSeconds;
                    
                    if (countdownSeconds <= 5) {
                        countdownEl.classList.add('low');
                    } else {
                        countdownEl.classList.remove('low');
                    }
                }
                
                if (countdownSeconds <= 0) {
                    clearInterval(countdownInterval);
                    showRefreshingAnimation();
                    fetch('/api/refresh')
                        .then(response => response.json())
                        .then(data => {
                            setTimeout(updateStatus, 1000);
                        })
                        .catch(error => {
                            console.error('Error refreshing status:', error);
                            updateStatus();
                        });
                }
            }, 1000);
        }

        function showRefreshingAnimation() {
            const countdownText = document.getElementById('countdownText');
            const refreshingAnimation = document.getElementById('refreshingAnimation');
            
            if (countdownText && refreshingAnimation) {
                countdownText.style.display = 'none';
                refreshingAnimation.style.display = 'flex';
            }
        }

        function hideRefreshingAnimation() {
            const countdownText = document.getElementById('countdownText');
            const refreshingAnimation = document.getElementById('refreshingAnimation');
            
            if (countdownText && refreshingAnimation) {
                countdownText.style.display = 'inline';
                refreshingAnimation.style.display = 'none';
            }
        }

        function refreshStatus() {
            clearInterval(countdownInterval);
            
            document.getElementById('loading').style.display = 'block';
            document.getElementById('statusGrid').style.display = 'none';
            
            showRefreshingAnimation();
            
            fetch('/api/refresh')
                .then(response => response.json())
                .then(data => {
                    setTimeout(updateStatus, 1000);
                })
                .catch(error => {
                    console.error('Error refreshing status:', error);
                    updateStatus();
                });
        }

        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            document.getElementById('autoRefreshStatus').textContent = autoRefresh ? 'ON' : 'OFF';
            
            if (autoRefresh) {
                startAutoRefresh();
                updateLastUpdated();
                startCountdown();
            } else {
                clearInterval(refreshInterval);
                clearInterval(countdownInterval);
                updateLastUpdated();
            }
        }

        function startAutoRefresh() {
            refreshInterval = setInterval(updateStatus, 30000);
        }

        updateStatus();
        if (autoRefresh) {
            startAutoRefresh();
        }
    </script>
</body>
</html>
"""

# Create templates directory and file
import os

if not os.path.exists("templates"):
    os.makedirs("templates")

with open("templates/index.html", "w", encoding="utf-8") as f:
    f.write(INDEX_HTML)

# Ensure basic logs template exists
logs_template_path = "templates/logs.html"
if not os.path.exists(logs_template_path):
    with open(logs_template_path, "w", encoding="utf-8") as f:
        f.write(
            """<!DOCTYPE html><html><head><meta charset='UTF-8'><title>Gateway Logs</title></head><body><h1>Gateway Logs</h1></body></html>"""
        )

if __name__ == "__main__":
    # Start background monitoring
    monitor_thread = threading.Thread(target=background_monitor, daemon=True)
    monitor_thread.start()

    print("Weather Push Service starting...")
    start_weather_push_service()
    # Initial status check
    print("Ignition Gateway Monitor starting...")
    monitor_all_gateways()

    print("Ignition Gateway Monitor starting...")
    print("Monitoring gateways:")
    for category, gateways in GATEWAYS.items():
        print(f"  {category}:")
        for gateway in gateways:
            web_port = gateway.get("web_port", "N/A")
            print(
                f"    - {gateway['name']} ({gateway['host']}:{gateway['port']}, web:{web_port})"
            )

    app.run(debug=True, host="0.0.0.0", port=5000)
