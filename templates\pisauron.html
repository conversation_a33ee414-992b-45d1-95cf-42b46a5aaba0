<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Pi of Sauron</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e1e2e 0%, #2d2d44 100%);
            color: #e0e0e0;
            padding: 20px;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            color: #4fc3f7;
            margin-bottom: 15px;
            font-size: 2.5em;
            text-shadow: 0 0 20px rgba(79, 195, 247, 0.3);
        }

        .line-selector {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }

        .line-tab {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(79, 195, 247, 0.3);
            color: #e0e0e0;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: 600;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .line-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(79, 195, 247, 0.1), transparent);
            transition: left 0.5s;
        }

        .line-tab:hover::before {
            left: 100%;
        }

        .line-tab.active {
            background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
            border-color: #4fc3f7;
            box-shadow: 0 0 30px rgba(79, 195, 247, 0.4);
            transform: translateY(-2px);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
        }

        .btn {
            background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 195, 247, 0.4);
        }

        .production-lines-container {
            display: flex;
            flex-direction: column;
            gap: 60px;
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .production-lines-container.flipped {
            flex-direction: column-reverse;
        }

        .production-line {
            margin: 0;
            opacity: 1;
            transform: translateY(0);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .production-line.focused {
            transform: scale(1.02);
            z-index: 10;
        }

        .production-line.unfocused {
            opacity: 0.7;
            transform: scale(0.98);
        }

        .line-header {
            text-align: center;
            margin-bottom: 30px;
            font-size: 1.5em;
            color: #4fc3f7;
            font-weight: 600;
        }

        .factory-layout {
            position: relative;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            transition: all 0.5s ease;
        }

        .production-line.focused .factory-layout {
            background: rgba(79, 195, 247, 0.08);
            border-color: rgba(79, 195, 247, 0.3);
            box-shadow: 0 0 30px rgba(79, 195, 247, 0.2);
        }

        .outside-wall-label {
            position: absolute;
            top: 15px;
            left: 50%;
            transform: translateX(-50%);
            color: #b0b0b0;
            font-size: 1.1em;
            font-weight: 500;
            border-bottom: 2px solid rgba(79, 195, 247, 0.3);
            padding-bottom: 5px;
        }

        .stations-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 25px;
            margin-bottom: 40px;
            margin-top: 40px;
        }

        .pack-line-area {
            background: linear-gradient(135deg, rgba(79, 195, 247, 0.05) 0%, rgba(41, 182, 246, 0.05) 100%);
            border: 2px dashed rgba(79, 195, 247, 0.2);
            border-radius: 15px;
            padding: 30px;
            position: relative;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .pack-line-label {
            position: absolute;
            bottom: 15px;
            right: 20px;
            color: #4fc3f7;
            font-size: 1.2em;
            font-weight: 600;
            opacity: 0.7;
        }

        .station-card {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            aspect-ratio: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .station-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4fc3f7, #29b6f6);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .station-card.online::before {
            background: linear-gradient(90deg, #4caf50, #66bb6a);
            opacity: 1;
        }

        .station-card.offline::before {
            background: linear-gradient(90deg, #f44336, #ef5350);
            opacity: 1;
        }

        .station-card.placeholder::before {
            background: linear-gradient(90deg, #ff9800, #ffa726);
            opacity: 1;
        }

        .station-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .station-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .station-id {
            font-size: 1.1em;
            font-weight: 600;
            color: #4fc3f7;
        }

        .station-number {
            font-size: 1.3em;
            font-weight: 700;
            color: #ffffff;
            background: rgba(79, 195, 247, 0.2);
            padding: 5px 10px;
            border-radius: 8px;
        }

        .status-indicator {
            font-weight: 600;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.9em;
        }

        .status-indicator.online {
            background: rgba(76, 175, 80, 0.2);
            color: #66bb6a;
        }

        .status-indicator.offline {
            background: rgba(244, 67, 54, 0.2);
            color: #ef5350;
        }

        .status-indicator.placeholder {
            background: rgba(255, 152, 0, 0.2);
            color: #ffa726;
        }

        .station-preview {
            width: 100%;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
            margin: 10px 0;
            background: rgba(255, 255, 255, 0.05);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #888;
            font-size: 0.9em;
        }

        .station-info {
            font-size: 0.85em;
            color: #b0b0b0;
            text-align: center;
        }

        .development-section {
            margin-top: 50px;
            padding: 30px;
            background: rgba(255, 152, 0, 0.05);
            border: 1px solid rgba(255, 152, 0, 0.2);
            border-radius: 15px;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .development-section.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .dev-header {
            text-align: center;
            margin-bottom: 20px;
            color: #ffa726;
            font-size: 1.2em;
            font-weight: 600;
        }

        .dev-grid {
            display: flex;
            justify-content: center;
            gap: 20px;
        }

        /* Modal styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: linear-gradient(135deg, #2d2d44 0%, #1e1e2e 100%);
            padding: 30px;
            border-radius: 15px;
            max-width: 90%;
            max-height: 90%;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        }

        .modal-content img {
            max-width: 100%;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .section {
            margin-bottom: 15px;
        }

        .section h4 {
            margin-bottom: 8px;
            color: #4fc3f7;
            font-size: 1.1em;
        }

        .section ul {
            list-style: none;
            padding-left: 0;
            margin: 0;
        }

        .section li {
            margin-bottom: 6px;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        .actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            justify-content: center;
        }

        .close-btn {
            background: #ef5350;
        }

        .delete-btn {
            background: #f44336;
        }

        .animate-in {
            animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .shuffle-out {
            animation: shuffleOut 0.4s ease-in forwards;
        }

        .shuffle-in {
            animation: shuffleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        @keyframes shuffleOut {
            to {
                opacity: 0;
                transform: translateY(-20px) scale(0.8);
            }
        }

        @keyframes shuffleIn {
            from {
                opacity: 0;
                transform: translateY(20px) scale(1.1);
            }

            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .station-card.rain-drop {
            animation: rainDrop 1.2s cubic-bezier(0.4, 0, 0.8, 1) forwards;
        }

        .station-card.rain-rise {
            animation: rainRise 1s cubic-bezier(0.2, 0, 0.4, 1) forwards;
        }

        @keyframes rainDrop {
            0% {
                transform: translateY(0) scale(1);
                opacity: 1;
            }

            100% {
                transform: translateY(150vh) scale(0.8);
                opacity: 0;
            }
        }

        @keyframes rainRise {
            0% {
                transform: translateY(150vh) scale(0.8);
                opacity: 0;
            }

            100% {
                transform: translateY(0) scale(1);
                opacity: 1;
            }
        }

        .reman-area {
            background: linear-gradient(135deg, rgba(156, 39, 176, 0.05) 0%, rgba(142, 36, 170, 0.05) 100%);
            border: 2px dashed rgba(156, 39, 176, 0.2);
            border-radius: 15px;
            padding: 40px;
            position: relative;
            min-height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 40px;
        }

        .reman-label {
            color: #ab47bc;
            font-size: 1.5em;
            font-weight: 600;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .stations-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .factory-layout {
                padding: 20px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>Pi of Sauron</h1>
            <p>Production Line Monitoring System</p>
        </div>

        <div class="line-selector">
            <div class="line-tab active" data-line="pack1">Pack Line 1</div>
            <div class="line-tab" data-line="pack2">Pack Line 2</div>
        </div>

        <div class="controls">
            <a class="btn" href="/">⬅️ Back</a>
            <button class="btn" onclick="refreshData()">🔄 Refresh</button>
        </div>

        <!-- Production Lines Container -->
        <div class="production-lines-container" id="production-container">
            <!-- Pack Line 1 -->
            <div class="production-line focused" id="pack1-line">
                <div class="line-header">Pack Line 1</div>
                <div class="factory-layout">
                    <div class="outside-wall-label">Outside Wall</div>
                    <div class="stations-grid" id="pack1-stations">
                        <!-- Stations will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Pack Line 2 -->
            <div class="production-line unfocused" id="pack2-line">
                <div class="line-header">Pack Line 2</div>
                <div class="factory-layout">
                    <div class="outside-wall-label">Outside Wall</div>
                    <div class="stations-grid" id="pack2-stations">
                        <!-- Will be populated when line 2 comes online -->
                    </div>
                </div>
            </div>

            <!-- Reman Area -->
            <div class="reman-area">
                <div class="reman-label">Reman Area</div>
            </div>
        </div>

        <!-- Development Section -->
        <div class="development-section" id="dev-section">
            <div class="dev-header">🔬 Development & Testing</div>
            <div class="dev-grid" id="dev-stations">
                <!-- Development stations will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div id="detailModal" class="modal" onclick="closeModal(event)">
        <div class="modal-content" id="modalContent" onclick="event.stopPropagation()"></div>
    </div>

    <script>
        let kiosksData = {};
        let currentLine = 'pack1';

        // Station configuration matching your physical layout
        const stationConfig = {
            pack1: [
                { id: 'GVP-RBP-PL1S3', station: 3, ip: '*************' },
                { id: 'GVP-RBP-PL1S2', station: 2, ip: '*************' },
                { id: 'GVP-RBP-PL1S1', station: 1, ip: '*************' },
                { id: 'GVP-RBP-PL1S8', station: 8, ip: '*************' },
                { id: 'GVP-RBP-PL1S4', station: 4, ip: '*************' },
                { id: 'GVP-RBP-PL1S5', station: 5, ip: '*************' },
                { id: 'GVP-RBP-PL1S6', station: 6, ip: '*************' },
                { id: 'GVP-RBP-PL1S7', station: 7, ip: '*************' }
            ],
            pack2: [
                { id: 'GVP-RBP-PL2S3', station: 3, ip: 'TBD' },
                { id: 'GVP-RBP-PL2S2', station: 2, ip: 'TBD' },
                { id: 'GVP-RBP-PL2S1', station: 1, ip: 'TBD' },
                { id: 'GVP-RBP-PL2S8', station: 8, ip: 'TBD' },
                { id: 'GVP-RBP-PL2S4', station: 4, ip: 'TBD' },
                { id: 'GVP-RBP-PL2S5', station: 5, ip: 'TBD' },
                { id: 'GVP-RBP-PL2S6', station: 6, ip: 'TBD' },
                { id: 'GVP-RBP-PL2S7', station: 7, ip: 'TBD' }
            ]
        };

        function fetchKiosks() {
            fetch('/api/kiosks/status')
                .then(r => r.json())
                .then(data => {
                    kiosksData = data;
                    renderStations();
                })
                .catch(err => {
                    console.error('Failed to fetch kiosks:', err);
                });
        }

        function isOnline(ts) {
            if (!ts) return false;
            const t = new Date(ts).getTime();
            return Date.now() - t < 5 * 60 * 1000; // 5 minutes
        }

        function createStationCard(stationInfo, agentData = null) {
            const isDevStation = stationInfo.id && stationInfo.id.includes('TEST');
            const online = agentData ? isOnline(agentData.timestamp) : false;
            const status = agentData ? (online ? 'online' : 'offline') : 'placeholder';

            const card = document.createElement('div');
            card.className = `station-card ${status} animate-in`;

            const stationNumber = stationInfo.station || 'DEV';
            const stationId = stationInfo.id || 'Unknown';
            const statusText = agentData ? (online ? 'Online' : 'Offline') : 'Not Connected';

            card.innerHTML = `
                <div class="station-header">
                    <div class="station-id">${stationId}</div>
                    <div class="station-number">${stationNumber}</div>
                </div>
                <div class="status-indicator ${status}">${statusText}</div>
                ${agentData && agentData.screenshot ?
                    `<img class="station-preview" src="data:image/png;base64,${agentData.screenshot}" alt="Station ${stationNumber}">` :
                    `<div class="station-preview">No Preview</div>`
                }
                <div class="station-info">
                    ${stationInfo.ip || 'IP: Not assigned'}
                    ${agentData ? `<br>Last: ${new Date(agentData.timestamp).toLocaleTimeString()}` : ''}
                </div>
            `;

            if (agentData) {
                card.onclick = () => showDetails(stationId);
            }

            return card;
        }

        function renderStations() {
            const pack1Container = document.getElementById('pack1-stations');
            const pack2Container = document.getElementById('pack2-stations');
            const devContainer = document.getElementById('dev-stations');

            // Clear containers
            pack1Container.innerHTML = '';
            pack2Container.innerHTML = '';
            devContainer.innerHTML = '';

            let hasDev = false;

            // Render Pack Line 1 stations
            stationConfig.pack1.forEach((stationInfo, index) => {
                setTimeout(() => {
                    const agentData = kiosksData[stationInfo.id];
                    const card = createStationCard(stationInfo, agentData);
                    pack1Container.appendChild(card);
                }, index * 100);
            });

            // Render Pack Line 2 stations
            stationConfig.pack2.forEach((stationInfo, index) => {
                setTimeout(() => {
                    const agentData = kiosksData[stationInfo.id];
                    const card = createStationCard(stationInfo, agentData);
                    pack2Container.appendChild(card);
                }, index * 100);
            });

            // Render development stations
            Object.keys(kiosksData).forEach(agentId => {
                if (agentId.includes('TEST') ||
                    (!stationConfig.pack1.some(s => s.id === agentId) &&
                        !stationConfig.pack2.some(s => s.id === agentId))) {
                    const agentData = kiosksData[agentId];
                    const stationInfo = { id: agentId, station: 'DEV' };
                    const card = createStationCard(stationInfo, agentData);
                    devContainer.appendChild(card);
                    hasDev = true;
                }
            });

            // Show/hide development section
            const devSection = document.getElementById('dev-section');
            if (hasDev) {
                setTimeout(() => {
                    devSection.classList.add('visible');
                }, 800);
            } else {
                devSection.classList.remove('visible');
            }
        }

        function switchLine(lineId) {
            const tabs = document.querySelectorAll('.line-tab');
            const container = document.getElementById('production-container');
            const pack1Line = document.getElementById('pack1-line');
            const pack2Line = document.getElementById('pack2-line');
            const currentFocused = document.querySelector('.production-line.focused');

            // Update tabs
            tabs.forEach(tab => {
                tab.classList.toggle('active', tab.dataset.line === lineId);
            });

            // Don't animate if same line is selected
            if (currentFocused && currentFocused.id === lineId + '-line') {
                return;
            }

            // Start rain drop animation on current focused line
            if (currentFocused) {
                const currentStations = currentFocused.querySelectorAll('.station-card');
                currentStations.forEach((station, index) => {
                    setTimeout(() => {
                        station.classList.add('rain-drop');
                    }, index * 100);
                });
            }

            // Wait for rain to start, then flip layout and focus
            setTimeout(() => {
                // Flip the container if switching to pack2
                if (lineId === 'pack2') {
                    container.classList.add('flipped');
                } else {
                    container.classList.remove('flipped');
                }

                // Update focus states
                pack1Line.classList.toggle('focused', lineId === 'pack1');
                pack1Line.classList.toggle('unfocused', lineId !== 'pack1');
                pack2Line.classList.toggle('focused', lineId === 'pack2');
                pack2Line.classList.toggle('unfocused', lineId !== 'pack2');

                // Start rain rise animation on newly focused line
                const newFocused = document.getElementById(lineId + '-line');
                const newStations = newFocused.querySelectorAll('.station-card');
                newStations.forEach((station, index) => {
                    station.classList.remove('rain-drop');
                    setTimeout(() => {
                        station.classList.add('rain-rise');
                    }, index * 80);
                });

                // Clean up animations after they complete
                setTimeout(() => {
                    document.querySelectorAll('.station-card').forEach(station => {
                        station.classList.remove('rain-drop', 'rain-rise');
                    });
                }, 1200);

            }, 300);

            currentLine = lineId;
        }

        function showDetails(agent) {
            const info = kiosksData[agent];
            if (!info) return;

            const health = info.health || {};
            const modal = document.getElementById('detailModal');
            const content = document.getElementById('modalContent');

            function formatObj(obj) {
                return Object.entries(obj || {})
                    .map(([k, v]) => `<li><strong>${k}:</strong> ${v}</li>`)
                    .join('');
            }

            const general = {
                ip: info.ip || (health.net || {}).ip,
                os: health.os,
                kernel: health.kernel,
                uptime_sec: health.uptime_sec,
                cpu_temp_c: health.cpu_temp_c,
                display_mode: health.display_mode,
            };

            content.innerHTML = `
                <h2>${agent}</h2>
                ${info.screenshot ? `<img src="data:image/png;base64,${info.screenshot}" alt="${agent} screenshot">` : ''}
                <div class="section"><h4>System</h4><ul>${formatObj(general)}</ul></div>
                <div class="section"><h4>Network</h4><ul>${formatObj(health.net || {})}</ul></div>
                <div class="section"><h4>Services</h4><ul>${formatObj(health.services || {})}</ul></div>
                <div class="section">
                    <h4>Health Monitor Log <button class="btn" onclick="loadMonitorLog('${agent}')" style="font-size: 0.8em; padding: 4px 8px; margin-left: 10px;">🔄 Refresh Log</button></h4>
                    <div id="monitor-log-${agent}" style="background: rgba(0,0,0,0.3); padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.85em; white-space: pre-wrap;">
                        Loading monitor log...
                    </div>
                </div>
                <div class="actions">
                    <button class="btn" onclick="sendCommand('${agent}', 'refresh')">🔄 Refresh</button>
                    <button class="btn" onclick="sendCommand('${agent}', 'reboot')">🔄 Reboot</button>
                    <button class="btn delete-btn" onclick="deletePi('${agent}')">💣 Delete</button>
                    <button class="btn close-btn" onclick="closeModal()">✖️ Close</button>
                </div>
            `;

            // Load monitor log automatically when modal opens
            loadMonitorLog(agent);
            modal.style.display = 'flex';
        }

        function closeModal(event) {
            if (!event || event.target === document.getElementById('detailModal')) {
                document.getElementById('detailModal').style.display = 'none';
            }
        }

        function sendCommand(agent, cmd) {
            fetch(`/api/kiosks/${agent}/command`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ cmd })
            })
                .then(r => r.json())
                .then(d => {
                    alert(d.ok ? 'Command sent successfully' : `Failed: ${d.error || 'unknown error'}`);
                })
                .catch(() => alert('Failed to send command'));
        }

        function deletePi(agent) {
            if (!confirm(`Remove ${agent}?`)) return;
            fetch(`/api/kiosks/${agent}`, { method: 'DELETE' })
                .then(r => r.json())
                .then(() => {
                    delete kiosksData[agent];
                    renderStations();
                    closeModal();
                });
        }

        function loadMonitorLog(agent) {
            const logContainer = document.getElementById(`monitor-log-${agent}`);
            if (!logContainer) return;

            logContainer.textContent = 'Loading monitor log...';

            fetch(`/api/kiosks/${agent}/monitor-log`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        logContainer.textContent = `Error: ${data.error}`;
                        logContainer.style.color = '#ef5350';
                    } else if (data.log_lines && data.log_lines.length > 0) {
                        // Show last 50 lines (or all if less than 50)
                        const lines = data.log_lines.slice(-50);
                        logContainer.textContent = lines.join('');
                        logContainer.style.color = '#e0e0e0';
                        // Scroll to bottom
                        logContainer.scrollTop = logContainer.scrollHeight;
                    } else {
                        logContainer.textContent = 'No monitor log data available';
                        logContainer.style.color = '#ffa726';
                    }
                })
                .catch(error => {
                    logContainer.textContent = `Failed to load monitor log: ${error.message}`;
                    logContainer.style.color = '#ef5350';
                });
        }

        function refreshData() {
            fetchKiosks();
        }

        // Event listeners
        document.querySelectorAll('.line-tab').forEach(tab => {
            tab.addEventListener('click', () => switchLine(tab.dataset.line));
        });

        // Initial load
        fetchKiosks();
        setInterval(fetchKiosks, 30000); // Refresh every 30 seconds
    </script>
</body>

</html>