## Additions adding to the raspi for checks
## add to kiosk_monitor.py


sudo tee /opt/kiosk-agent/kiosk_monitor.py << 'EOF'
#!/usr/bin/env python3
import os
import sys
import time
import subprocess
import requests
import socket
import json
from datetime import datetime

# Configuration
CHECK_INTERVAL = 60  # seconds between health checks
NETWORK_TIMEOUT = 10  # seconds for network tests
REBOOT_THRESHOLD = 3  # failed checks before reboot
LOG_FILE = "/var/log/kiosk-monitor.log"

# URLs and endpoints to check (2 out of 4 must pass)
CONNECTIVITY_TESTS = [
    ("Ignition Gateway", "https://10.110.170.7:8043/data/perspective/client/REVMES_Powered/countdownClock/"),
    ("Pi of Sauron Server", "http://10.14.150.230:5000"),
    ("Internet Connectivity", "https://www.google.com"),
    ("Network Gateway", "ping://10.14.150.1"),  # Special ping test
]
MIN_CONNECTIVITY_REQUIRED = 2  # At least 2 of 4 must pass

class KioskMonitor:
    def __init__(self):
        self.failed_checks = 0
        
    def log(self, message, level="INFO"):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_msg = f"[{timestamp}] {level}: {message}"
        print(log_msg)
        
        try:
            with open(LOG_FILE, "a") as f:
                f.write(log_msg + "\n")
        except:
            pass
    
    def run_command(self, cmd, timeout=10):
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, 
                                  text=True, timeout=timeout)
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", "Timeout"
        except Exception as e:
            return False, "", str(e)
    
    def check_network_connectivity(self):
        """Test basic network connectivity with flexible requirements"""
        passed_tests = 0
        total_tests = len(CONNECTIVITY_TESTS)
        
        for test_name, test_target in CONNECTIVITY_TESTS:
            try:
                if test_target.startswith("ping://"):
                    # Ping test
                    host = test_target.replace("ping://", "")
                    success, _, _ = self.run_command(f"ping -c 2 -W 3 {host}", timeout=10)
                    if success:
                        passed_tests += 1
                        self.log(f"Connectivity test passed: {test_name} ({host})", "DEBUG")
                    else:
                        self.log(f"Connectivity test failed: {test_name} ({host}) - ping failed", "ERROR")
                else:
                    # HTTP/HTTPS test
                    response = requests.get(test_target, timeout=NETWORK_TIMEOUT, 
                                          verify=False, allow_redirects=True)
                    if response.status_code < 500:
                        passed_tests += 1
                        self.log(f"Connectivity test passed: {test_name}", "DEBUG")
                    else:
                        self.log(f"Connectivity test failed: {test_name} - HTTP {response.status_code}", "ERROR")
            except Exception as e:
                self.log(f"Connectivity test failed: {test_name} - {e}", "ERROR")
        
        success = passed_tests >= MIN_CONNECTIVITY_REQUIRED
        self.log(f"Connectivity summary: {passed_tests}/{total_tests} tests passed (need {MIN_CONNECTIVITY_REQUIRED})", 
                "DEBUG" if success else "ERROR")
        
        return success
    
    def check_chromium_running(self):
        """Check if Chromium is running and responsive"""
        try:
            # Check if chromium process exists
            success, output, _ = self.run_command("pgrep -f chromium-browser")
            if not success:
                self.log("Chromium process not found", "ERROR")
                return False
            
            # Check if debugging port is responding
            try:
                response = requests.get("http://127.0.0.1:9222/json", timeout=5)
                if response.status_code == 200:
                    targets = response.json()
                    if not targets:
                        self.log("No Chromium targets found", "ERROR")
                        return False
                    return True
            except:
                self.log("Chromium debugging port not responding", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Chromium check failed: {e}", "ERROR")
            return False
    
    def check_weston_running(self):
        """Check if Weston is running"""
        try:
            success, _, _ = self.run_command("pgrep -f weston")
            if not success:
                self.log("Weston process not found", "ERROR")
                return False
            
            # Check if wayland socket exists
            if not os.path.exists("/run/user/1000/wayland-0"):
                self.log("Wayland socket not found", "ERROR")
                return False
                
            return True
        except Exception as e:
            self.log(f"Weston check failed: {e}", "ERROR")
            return False
    
    def check_services_status(self):
        """Check if systemd services are active"""
        services = ["<EMAIL>", "kiosk.service"]
        
        for service in services:
            success, output, _ = self.run_command(f"systemctl is-active {service}")
            if not success or "active" not in output:
                self.log(f"Service {service} not active", "ERROR")
                return False
        return True
    
    def perform_health_check(self):
        """Perform comprehensive health check"""
        checks = [
            ("Network Connectivity", self.check_network_connectivity),
            ("Weston Running", self.check_weston_running),
            ("Chromium Running", self.check_chromium_running),
            ("Services Status", self.check_services_status),
        ]
        
        failed_checks = []
        
        for check_name, check_func in checks:
            try:
                if not check_func():
                    failed_checks.append(check_name)
                    self.log(f"Health check failed: {check_name}", "ERROR")
                else:
                    self.log(f"Health check passed: {check_name}", "DEBUG")
            except Exception as e:
                failed_checks.append(check_name)
                self.log(f"Health check error {check_name}: {e}", "ERROR")
        
        return len(failed_checks) == 0, failed_checks
    
    def restart_services(self):
        """Attempt to restart services before full reboot"""
        self.log("Attempting service restart", "INFO")
        
        commands = [
            "<NAME_EMAIL>",
            "sleep 3", 
            "systemctl restart kiosk.service",
            "systemctl restart kiosk-agent.service"
        ]
        
        for cmd in commands:
            success, _, error = self.run_command(f"sudo {cmd}", timeout=30)
            if not success:
                self.log(f"Service restart failed: {cmd} - {error}", "ERROR")
                return False
        
        self.log("Services restarted successfully", "INFO")
        return True
    
    def reboot_system(self):
        """Reboot the system"""
        self.log("REBOOTING SYSTEM due to persistent failures", "CRITICAL")
        subprocess.run(["sudo", "/sbin/reboot"], timeout=10)
    
    def run(self):
        self.log("Kiosk Monitor started", "INFO")
        
        while True:
            try:
                healthy, failed_checks = self.perform_health_check()
                
                if healthy:
                    if self.failed_checks > 0:
                        self.log(f"System recovered after {self.failed_checks} failed checks", "INFO")
                    self.failed_checks = 0
                else:
                    self.failed_checks += 1
                    self.log(f"Health check failed ({self.failed_checks}/{REBOOT_THRESHOLD}): {', '.join(failed_checks)}", "WARNING")
                    
                    if self.failed_checks >= REBOOT_THRESHOLD:
                        # Try service restart first (only once)
                        if self.failed_checks == REBOOT_THRESHOLD:
                            if self.restart_services():
                                self.log("Service restart attempted, monitoring continues", "INFO")
                                time.sleep(30)  # Give services time to start
                                continue
                        
                        # If we reach here, services restart failed or this is a subsequent failure
                        self.reboot_system()
                        break  # This line won't execute due to reboot, but good practice
                
                time.sleep(CHECK_INTERVAL)
                
            except KeyboardInterrupt:
                self.log("Monitor stopped by user", "INFO")
                break
            except Exception as e:
                self.log(f"Monitor error: {e}", "ERROR")
                time.sleep(CHECK_INTERVAL)

if __name__ == "__main__":
    monitor = KioskMonitor()
    monitor.run()
EOF



sudo chown display:display /opt/kiosk-agent/kiosk_monitor.py
sudo chmod +x /opt/kiosk-agent/kiosk_monitor.py

## create service

sudo tee /etc/systemd/system/kiosk-monitor.service << 'EOF'
[Unit]
Description=Kiosk Health Monitor and Auto-Recovery
After=kiosk.service kiosk-agent.service
Wants=kiosk.service kiosk-agent.service

[Service]
Type=simple
User=display
Group=display
Environment=XDG_RUNTIME_DIR=/run/user/1000
ExecStart=/opt/kiosk-agent/venv/bin/python /opt/kiosk-agent/kiosk_monitor.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# Allow the service to run system commands for restart/reboot
NoNewPrivileges=false

[Install]
WantedBy=multi-user.target
EOF

## setup permissions
# Allow display user to restart services and reboot without password

sudo tee /etc/sudoers.d/kiosk-monitor << 'EOF'
display ALL=(ALL) NOPASSWD: /bin/<NAME_EMAIL>
display ALL=(ALL) NOPASSWD: /bin/systemctl restart kiosk.service  
display ALL=(ALL) NOPASSWD: /bin/systemctl restart kiosk-agent.service
display ALL=(ALL) NOPASSWD: /sbin/reboot
EOF

## log roto 

sudo tee /etc/logrotate.d/kiosk-monitor << 'EOF'
/var/log/kiosk-monitor.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    copytruncate
    create 644 display display
}
EOF

# Install requests if not in venv yet
sudo -u display /opt/kiosk-agent/venv/bin/pip install requests

# Enable and start the monitor service
sudo systemctl daemon-reload
sudo systemctl enable kiosk-monitor.service
sudo systemctl start kiosk-monitor.service

# Check status
sudo systemctl status kiosk-monitor.service

# Check the monitor logs
sudo tail -f /var/log/kiosk-monitor.log

# Test the monitor by stopping a service temporarily
sudo systemctl stop kiosk.service
# Watch logs - should detect failure and restart service

# Check monitor service status
sudo systemctl status kiosk-monitor.service